/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react'
import { getFfrStatistics } from '@/api/qms/manufacture-board/quality'
import { GeneralData, SpecialData, KeyProject } from '@/views/qms/billboard/interface'
import { getLimitKeyProject } from '../config'
import { BOARD_TYPE } from '../constant'
import type { FfrChartData } from '../interface'
import { DATE_TYPE } from '@/views/qms/billboard/constant'

// 定义接口用于请求参数
export interface DealBoardDataParams {
  productLineId: string | number
  brandClassName: string
  countryName: string
  dateTypeGeneral: typeof DATE_TYPE.WEEK | typeof DATE_TYPE.MONTH
  dateTypeKeyProject: typeof DATE_TYPE.DAY | typeof DATE_TYPE.WEEK | typeof DATE_TYPE.MONTH
  dateGeneral?: string
}

// 定义看板数据类型
type BoardDataItem = {
  boardName: typeof BOARD_TYPE.GENERAL | typeof BOARD_TYPE.SPECIAL | typeof BOARD_TYPE.KEY_PROJECTS
  isEmpty: boolean
  data: GeneralData | SpecialData | KeyProject[]
}

type BoardData = BoardDataItem[]

function controlLength(controlData, limitLength, type) {
  if (controlData.dataList.length > limitLength) {
    controlData.dataList.splice(limitLength)
  } else {
    if (type === BOARD_TYPE.GENERAL) {
      // 标记上一周期数据位置
      const index = controlData.dataList.length < 2 ? 0 : controlData.dataList.length - 2
      controlData.dataList[index] && (controlData.dataList[index].isLastPeriod = true)
    }
    if (type === BOARD_TYPE.SPECIAL && controlData.dataList[controlData.dataList.length - 1]) {
      // 标记最近周期数据位置
      controlData.dataList[controlData.dataList.length - 1].isRecentlyPeriod = true
    }
    while (controlData.dataList.length < limitLength) {
      controlData.dataList.push({
        fmNum: undefined,
        fzNum: undefined,
        rate: undefined,
        index: controlData.dataList.length + 1,
      })
    }
  }
}
// 自定义 Hook
const useFfrBoardData = (
  params: DealBoardDataParams
): {
  isLoading: boolean
  boardData: BoardData
} => {
  const [boardData, setBoardData] = useState<BoardData>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      const noBoardData = () => {
        setBoardData([
          { boardName: BOARD_TYPE.GENERAL, isEmpty: true, data: {} as GeneralData },
          { boardName: BOARD_TYPE.SPECIAL, isEmpty: true, data: {} as SpecialData },
          { boardName: BOARD_TYPE.KEY_PROJECTS, isEmpty: true, data: [] as KeyProject[] },
        ])
      }
      try {
        setIsLoading(true)

        const { data: responseData, code } = await getFfrStatistics(params)
        const { brandClassData } = responseData || {}

        if (code === 0 && brandClassData) {
          const { general, keyProjects = [] } = brandClassData

          // 大盘
          if (Array.isArray(general?.trendChartData) && general?.trendChartData.length) {
            const allEmpty = general?.trendChartData.every((item) => item.dataList.length === 0)
            if (allEmpty) {
              general.trendChartData = null
            } else {
              general?.trendChartData.forEach((item) => {
                controlLength(item, getLimitKeyProject[params.dateTypeGeneral], BOARD_TYPE.GENERAL)
              })
            }
          } else {
            general.trendChartData = null
          }

          // 项目
          let special = {} as FfrChartData
          let isExistProjectTrendChartData = false
          if (Array.isArray(keyProjects)) {
            // 三级故障
            special = { barChartData: keyProjects[0].fault3 } as FfrChartData
            // 重点项目
            keyProjects.forEach((item: KeyProject) => {
              const { projectData } = item
              if (projectData && projectData.trendChartData) {
                isExistProjectTrendChartData = true
                projectData.trendChartData.forEach((element) => {
                  controlLength(
                    element,
                    getLimitKeyProject[params.dateTypeKeyProject],
                    BOARD_TYPE.SPECIAL
                  )
                })
              }
            })
          }

          // 三级故障
          if (!Array.isArray(special?.barChartData)) {
            special.barChartData = []
          }

          setBoardData([
            {
              boardName: BOARD_TYPE.GENERAL,
              isEmpty: !general.trendChartData.length,
              data: general,
            },
            { boardName: BOARD_TYPE.SPECIAL, isEmpty: !special.barChartData.length, data: special },
            {
              boardName: BOARD_TYPE.KEY_PROJECTS,
              isEmpty: !keyProjects.length || !isExistProjectTrendChartData,
              data: keyProjects,
            },
          ])
        } else {
          noBoardData()
        }
      } catch (err) {
        console.error('数据获取失败:', err)
        noBoardData()
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [
    params.productLineId,
    params.countryName,
    params.brandClassName,
    params.dateTypeGeneral,
    params.dateTypeKeyProject,
    params.dateGeneral,
  ])

  return { isLoading, boardData }
}

export default useFfrBoardData
