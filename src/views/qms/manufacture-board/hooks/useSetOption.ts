/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import { getXlineOption } from '@/views/qms/billboard/hooks/useSetOption'
import { CHART_X_AXIS_MIN_VALUE, BOARD_DISPLAY_NAMES } from '@/views/qms/billboard/constant'

import type { EChartsOption } from 'echarts'
import { getBarConfig, getPieConfig } from '../echarts/mfgdi'
import { getLineConfig, getYBarConfig } from '../echarts/fpy'
import { getLineOption, getBarOption } from '../echarts/ffr'
import { CHART_TYPE } from '../constant'

export const getDefaultOption = (data, config, controlXText): echarts.EChartsOption => {
  switch (config.chartType) {
    // ffr
    case CHART_TYPE.GENERAL:
    case CHART_TYPE.KEY_PROJECTS:
      return getLineOption(data, config, controlXText)
    case CHART_TYPE.SPECIAL:
      return getBarOption(data, config)
    // mfg
    case CHART_TYPE.MFG_BAR:
      return getBarConfig(data)
    case CHART_TYPE.MFG_PIE:
      return getPieConfig(data)
    // fpy
    case CHART_TYPE.FPY_LINE:
      return getLineConfig(data)
    case CHART_TYPE.FPY_BAR:
      return getYBarConfig(data)
    default:
      return {}
  }
}

const getEchartsOption = ({
  data,
  config,
  controlXText,
}): {
  option: echarts.EChartsOption
  graphic: any
} => {
  const getOption = () => {
    let updateOption = getDefaultOption(data, config, controlXText)

    const { data: xAxisData, type } =
      (updateOption?.xAxis as EChartsOption['xAxis'] as {
        data: string[]
        type: string
      }) ?? {}

    if (xAxisData && xAxisData?.length > 12 && type !== 'time') {
      updateOption = {
        ...updateOption,
        xAxis: {
          ...(updateOption?.xAxis ?? {}),
          axisLabel: {
            interval: 0,
            formatter: function (value, index) {
              return controlXText.includes(index + 1) ? value : ''
            },
          },
        } as EChartsOption['xAxis'],
      }
    }
    if (config.chartType === CHART_TYPE.SPECIAL) {
      // 让x轴的最大值不小于xAxisMaxValue 0.000003
      // 为了让横坐标至少有5个值
      const xAxisMaxValue = CHART_X_AXIS_MIN_VALUE
      let xAxisMax = 0
      if (Array.isArray(updateOption?.series)) {
        updateOption.series.forEach?.((item: any) => {
          item.data.forEach((i) => {
            if (i && typeof i === 'number' && i > xAxisMax) {
              xAxisMax = i
            }
          })
        })
      }

      if (updateOption?.xAxis && !Array.isArray(updateOption?.xAxis)) {
        const max = Math.max(xAxisMax, xAxisMaxValue)

        /**
         * 简化设置X轴刻度，确保只有5个刻度点
         * 通过设置min=0, max和interval来控制刻度数量
         */
        updateOption.xAxis.min = 0
        updateOption.xAxis.max = max

        // 计算interval以确保正好显示5个刻度值(0, 1/4*max, 2/4*max, 3/4*max, max)
        const interval = max / 4

        // 使用类型断言设置interval
        const xAxisAny = updateOption.xAxis as any
        xAxisAny.interval = interval

        // 移除可能造成干扰的配置
        delete xAxisAny.splitNumber
      }

      // 专项的y坐标, 可以去掉里面的专项
      const yAxisAny = updateOption.yAxis as any
      if (Array.isArray(yAxisAny?.data)) {
        yAxisAny.data = yAxisAny.data.map((i) => i?.replace(BOARD_DISPLAY_NAMES.SPECIAL, '') || '')
      }
    }

    return updateOption
  }

  return {
    option: getOption(),
    graphic: (childRef, { data, config }) => {
      return getXlineOption({ data, ChartType: config }, childRef)
    },
  }
}

export default getEchartsOption
