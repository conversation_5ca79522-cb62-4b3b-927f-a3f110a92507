/* eslint-disable @typescript-eslint/no-explicit-any */
import { QMS_IMG_PREFIX } from '@/constants'
const iconPrefix = `${QMS_IMG_PREFIX}billboard/`
const ManufactureIconPrefix = `${QMS_IMG_PREFIX}manufacture/`
export const downIcon = `${iconPrefix}downIcon.png`
export const TemperatureIcon = `${ManufactureIconPrefix}temperature.png`
export const HumidityIcon = `${ManufactureIconPrefix}humidity.png`
export const dustIcon = `${ManufactureIconPrefix}dust.png`
export const ESDIcon = `${ManufactureIconPrefix}ESD.png`
export const OrderIcon = `${ManufactureIconPrefix}order.png`
export const RateIcon = `${ManufactureIconPrefix}rate.png`
export const ValidFileIcon = `${ManufactureIconPrefix}validFile.png`

// 人员分布图表颜色常量
export const STAFF_CHART_COLORS = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE']
export interface ColumnType {
  title: string
  dataKey?: string
  dataIndex?: string
  width?: number
  render?: (value: any, row: any, index: number) => React.ReactNode
}

export interface TableItemType {
  id?: string | number
  [key: string]: any
}

export interface MetricsData {
  complianceRate?: number // 符合率
  complianceCount?: number // 点检符合数
  totalCount?: number // 点检总数
}

export const MACH_COLS = [
  { title: '工厂', dataIndex: 'factory' },
  { title: '项目', dataIndex: 'project' },
  { title: '工站', dataIndex: 'workstation' },
  { title: '关键项目', dataIndex: 'keyItem' },
  { title: '标准参数', dataIndex: 'standardParam' },
  { title: '实际参数', dataIndex: 'actualParam' },
]

export const DOC_COLS = [
  { title: '工厂', dataIndex: 'factory' },
  { title: '项目', dataIndex: 'project' },
  { title: '类型', dataIndex: 'type' },
  { title: '标准参数', dataIndex: 'standardParam' },
  { title: '实际参数', dataIndex: 'actualParam' },
]

// 图表卡片常量
export const CHART_CARD_CONSTANTS = {
  // 物料LAR图表样式
  MATERIAL_LAR: {
    color: '#0189FF',
    gradient:
      'linear-gradient(180deg, rgba(1, 111, 254, 0.13) 11.1%, rgba(1, 143, 255, 0.013) 74.34%, rgba(1, 159, 255, 0) 105.44%)',
    label: '物料LAR',
  },
  // 物料VLRR图表样式
  MATERIAL_VLRR: {
    color: '#FF7715',
    gradient:
      'linear-gradient(180deg, rgba(255, 144, 51, 0.13) 8.68%, rgba(255, 144, 51, 0.013) 77.14%, rgba(255, 144, 51, 0) 110.81%)',
    label: '物料VLRR',
  },
}

// 图表系列名称
export const CHART_SERIES_NAME = 'O2'

// UI文本常量
export const PERSON_TEXTS = {
  STAFF_RESERVE: '人员储备率',
  REQUIRED_COUNT: '需求人数',
  CURRENT_COUNT: '现有人数',
  STAFF_DISTRIBUTION: '在职人员分布',
}

// 标题组件常量
export const TITLE_TEXTS = {
  DEFAULT_TOOLTIP: '点击查看详情',
}

// 模块名称和tooltip常量
export const TOOLTIP_TEXTS = {
  STAFF_RESERVE: '人员储备率=需求人数/现有人数，要求120%储备率',
  MACHINE_COMPLIANCE:
    '符合率=点检符合数÷点检总数\n指的是工厂关键设备参数（如压力参数，胶重等）符合标准的比例',
  MATERIAL:
    'LAR：(Lot Acceptance Rate),代工厂来料检验批合格率\nLAR=(合格批数/总检验批数)*100%\nVLRR：(Verified Line Reject Rate),确认后的在线不良批退率\nVLRR=确认后的不良数/总投入*1000000',
  DOC_COMPLIANCE: '符合率=点检符合数÷点检总数',
}

// 图表相关常量
export const PERSON_CHART_TEXTS = {
  MAIN_TITLE: '检验能力看板',
  SUB_TITLE: '该模块只受"日期"控制，不受"品类、工厂、项目"的控制',
  GRADE_0: 'A+等级',
  GRADE_A: 'A等级',
  GRADE_B: 'B等级',
  GRADE_C: 'C等级',
  GRADE_D: 'D等级',
  MISS_RATE: '漏检率',
  GRADE_VOLUME: '检验人数',
  RATE_UNIT: '百分比 (%)',
}

// 图表相关常量
export const METRICS_TEXTS = {
  COMPLIANCE_RATE: '符合率',
  COMPLIANCE_COUNT: '点检符合数',
  TOTAL_COUNT: '点检总数',
}

// 图表颜色常量
export const ABILITY_CHART_COLORS = {
  GRADE_A: '#96ceff',
  GRADE_B: '#4a9eff',
  MISS_RATE: '#ff700d',
}

export const ECHART_COLORS = {
  axis: '#9299A6',
  legendText: '#5F6A7A',
  legendValue: '#1F2733',
  splitLine: '#EBEDF0A6',
}

// 排名颜色常量
export const RANKING_COLORS = {
  FIRST: '#FF9991',
  SECOND: '#FEC833',
  DEFAULT: '#B5BCC7',
}

// 空数据提示文本
export const EMPTY_DATA_TEXT = '暂无数据'

// 环境监控模块常量数据
export const ENVIRONMENT_MODULE_CONSTANTS = [
  {
    title: '温度达标率',
    icon: TemperatureIcon,
    description: '温度达标率 = 温度符合项 / 温度检查项 × 100%',
  },
  {
    title: '湿度达标率',
    icon: HumidityIcon,
    description: '湿度达标率 = 湿度符合项 / 湿度检查项 × 100%',
  },
  {
    title: '落尘达标率',
    icon: dustIcon,
    description: '落尘达标率 = 落尘符合项 / 落尘检查项 × 100%',
  },
  {
    title: 'ESD达标率',
    icon: ESDIcon,
    description: 'ESD达标率 = ESD符合项 / ESD检查项 × 100%',
  },
]

// 环境监控默认变量数据
export const ENVIRONMENT_DEFAULT_VARIABLE_DATA = {
  value: 0,
  standardRange: '≥ 95%',
  rankings: [],
}

export const personData = {
  reserveRate: '11%',
  requiredCount: 10,
  currentCount: 8,
  chartData: {
    days: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    evaporationData: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6],
    precipitationData: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6],
    temperatureData: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3],
  },
  chartTexts: {
    grade0: PERSON_CHART_TEXTS.GRADE_0,
    gradeA: PERSON_CHART_TEXTS.GRADE_A,
    gradeB: PERSON_CHART_TEXTS.GRADE_B,
    gradeC: PERSON_CHART_TEXTS.GRADE_C,
    gradeD: PERSON_CHART_TEXTS.GRADE_D,
    missRate: PERSON_CHART_TEXTS.MISS_RATE,
    gradeVolume: PERSON_CHART_TEXTS.GRADE_VOLUME,
  },
}
