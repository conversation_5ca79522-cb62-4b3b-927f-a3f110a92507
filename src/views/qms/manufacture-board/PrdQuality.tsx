import React, { memo, useMemo, useState } from 'react'
import { DIMENSION_TYPES } from './constant'
import BoardLine from './components/BoardLine'
import QualityPhone from './components/QualityPhone'
import QualityFfr from './components/QualityFfr'
import type { BaseTabProps, BoardItemData, DimensionProp } from './interface'

import { getFfrSelect } from '@/api/qms/manufacture-board/quality'
import { useRequest } from 'ahooks'

interface FfrTabProps {
  productLineId: number
  productLineName: string
  brandClassNameList: string
  countryNameList: string
}

// 设备类型常量
const DEVICE_TYPES = ['手机', '平板'] as const

// 手机产品线维度配置
const PHONE_DIMENSIONS: DimensionProp[] = [
  {
    ...DIMENSION_TYPES.REGION,
    list: ['国内', '国际', '印度'],
  },
  {
    ...DIMENSION_TYPES.FACTORY,
    limit: 3,
    list: ['昌平', '蓝思', '华勤', '龙旗'],
  },
  {
    ...DIMENSION_TYPES.PROJECT,
    limit: 3,
    list: ['O1', 'O2', 'P1', 'P3', 'P4', 'N12', 'O16'],
  },
]

// FFR产品线维度配置
const FFR_DIMENSIONS: DimensionProp[] = [
  {
    ...DIMENSION_TYPES.REGION,
    list: ['中国大陆', '全球(除中国、印度)', '印度'],
  },
]

// 产品线配置工厂函数
export const createBoardItemData = (): BoardItemData[] => [
  {
    productLineName: DIMENSION_TYPES.PHONE.title,
    productLineId: 1,
    deviceTypes: [...DEVICE_TYPES],
    dimensions: PHONE_DIMENSIONS,
    defaultNavBar: {
      deviceType: '手机',
      region: '国内',
      factory: '昌平',
      project: 'O1',
    },
    renderContent: QualityPhone,
  },
  {
    productLineName: DIMENSION_TYPES.FFR.title,
    productLineId: 2,
    deviceTypes: [...DEVICE_TYPES],
    dimensions: FFR_DIMENSIONS,
    defaultNavBar: {
      deviceType: '',
      region: '',
    },
    renderContent: QualityFfr,
  },
]

const getCascadeSelect = function (data, key) {}

const PrdQuality: React.FC<BaseTabProps> = (props) => {
  // 获取ffr筛选条件
  const [ffrTabs, setFfrTabs] = useState<FfrTabProps[]>([])
  useRequest(getFfrSelect, {
    manual: false,
    onSuccess: ({ data }) => {
      if (data && data.length) setFfrTabs(data)
    },
    onError: (err) => {
      console.log('🚀 ~ getFfrSelect err:', err)
    },
  })

  const boardData = useMemo(() => {
    // todo 数据需要转换级联
    console.log('ddd ==> props', props.child)
    const list = createBoardItemData()
    // 顶部tab
    list[0] = Object.assign(list[0], {
      deviceTypes: props.child?.map((item) => item.title) || [],
      dimensions: [],
    })
    // ffr的tab
    const ffrTab = ffrTabs && ffrTabs[0]
    console.log('ddd ==> ffrTab', ffrTab)

    if (ffrTab) {
      list[1] = Object.assign(list[1], {
        deviceTypes: ffrTab.brandClassNameList,
        dimensions: [
          {
            ...DIMENSION_TYPES.REGION,
            list: ffrTab.countryNameList,
          },
        ],
        defaultNavBar: {
          deviceType: ffrTab.brandClassNameList[0],
          region: ffrTab.countryNameList[0],
        },
      })
    }
    return list
  }, [props.child, ffrTabs])

  return (
    <>
      {boardData.map((item) => (
        <BoardLine key={item.productLineId} tab={item} renderContent={item.renderContent} />
      ))}
    </>
  )
}

PrdQuality.displayName = 'PrdQuality'

export default memo(PrdQuality)
