import React from 'react'
import Table from '@hi-ui/table'
import { ColumnType, TableItemType } from '../monitor-constant'

interface Props {
  columns: ColumnType[]
  tableData: TableItemType[]
  maxHeight?: number
}

export const InvalidTable = ({ columns, tableData, maxHeight = 480 }: Props) => {
  // 处理 dataIndex 和 dataKey 的兼容性
  const normalizedColumns = React.useMemo(() => {
    return columns.map((column) => {
      // 如果有 dataIndex 但没有 dataKey，则使用 dataIndex 作为 dataKey
      if (column.dataIndex && !column.dataKey) {
        return {
          ...column,
          dataKey: column.dataIndex,
        }
      }
      return column
    })
  }, [columns])

  return (
    <>
      <div className="monitor-card__divider">不符合明细</div>
      <div className="monitor-card__table">
        <Table
          columns={normalizedColumns}
          data={tableData}
          scrollbar={{ zIndex: 9 }}
          maxHeight={maxHeight}
          bordered
          emptyContent="暂无数据"
        />
      </div>
    </>
  )
}

InvalidTable.displayName = 'InvalidTable'

export default InvalidTable
