import React, { memo } from 'react'
import '../monitor.scss'
import AbilityChart from './AbilityChart'
import Tooltip from '@hi-ui/tooltip'
import { InfoCircleOutlined } from '@hi-ui/icons'
import StaffDistributionChart from './StaffDistributionChart'
import { PERSON_TEXTS, TOOLTIP_TEXTS } from '../monitor-constant'

interface MonitorPersonProps {
  reserveRate: string
  requiredCount: number
  currentCount: number
  chartData: {
    days: string[]
    evaporationData: number[]
    precipitationData: number[]
    temperatureData: number[]
  }
  chartTexts: {
    gradeA: string
    gradeB: string
    missRate: string
    gradeVolume: string
  }
}

const MonitorPerson = memo<MonitorPersonProps>(
  ({ reserveRate, requiredCount, currentCount, chartData, chartTexts }) => {
    const formulaTooltip = TOOLTIP_TEXTS.STAFF_RESERVE

    return (
      <div className="monitor-person">
        <div className="ability-board">
          <AbilityChart chartData={chartData} chartTexts={chartTexts} />
        </div>
        <div className="monitor-person__right">
          <div className="staff-reserve">
            <div className="staff-reserve__title">
              {PERSON_TEXTS.STAFF_RESERVE}
              <Tooltip title={formulaTooltip} placement="top">
                <span className="staff-reserve__info-icon">
                  <InfoCircleOutlined size={16} />
                </span>
              </Tooltip>
            </div>
            <div className="staff-reserve__content">{reserveRate}</div>
            <div className="staff-reserve__tip">
              <div className="staff-reserve__tip-title">{PERSON_TEXTS.REQUIRED_COUNT}</div>
              <div className="staff-reserve__tip-value">{requiredCount}</div>
              <div className="staff-reserve__tip-line"></div>
              <div className="staff-reserve__tip-title">{PERSON_TEXTS.CURRENT_COUNT}</div>
              <div className="staff-reserve__tip-value">{currentCount}</div>
            </div>
          </div>
          <div className="staff-distribution">
            <div className="staff-distribution__title">{PERSON_TEXTS.STAFF_DISTRIBUTION}</div>
            <div className="staff-distribution__content">
              <StaffDistributionChart />
            </div>
          </div>
        </div>
      </div>
    )
  }
)

MonitorPerson.displayName = 'MonitorPerson'
export default MonitorPerson
