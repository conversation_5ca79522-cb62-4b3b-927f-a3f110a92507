import React, { memo, useState, useMemo, useCallback, useEffect } from 'react'
import { BOARD_LINE_STYLE } from '../constant'
import NavLine from './NavLine'
import { iconMap } from '../config'
import type { ContentParams, PrdBoardLineProps, ActiveNavBar } from '../interface'

// 标题样式常量
const TITLE_STYLES = {
  container: {
    backgroundColor: BOARD_LINE_STYLE.TITLE_BACKGROUNDCOLOR,
  },
  title: {
    fontWeight: BOARD_LINE_STYLE.TITLE_FONT_WEIGHT,
    fontSize: BOARD_LINE_STYLE.TITLE_FONT_SIZE,
    color: BOARD_LINE_STYLE.TITLE_COLOR,
  },
  icon: {
    width: BOARD_LINE_STYLE.ICON_SIZE,
    height: BOARD_LINE_STYLE.ICON_SIZE,
    marginRight: BOARD_LINE_STYLE.ICON_MARGIN_RIGHT,
  },
}

const BoardLine: React.FC<PrdBoardLineProps> = ({ tab, renderContent }) => {
  const { productLineName, deviceTypes, dimensions, defaultNavBar } = tab
  const [activeNavBar, setActiveNavBar] = useState<ActiveNavBar>(defaultNavBar)

  useEffect(() => {
    setActiveNavBar(defaultNavBar)
  }, [defaultNavBar])

  // 导航栏状态更新处理函数
  const handleNavBarChange = useCallback((newNavBar: ActiveNavBar, key: string) => {
    setActiveNavBar(newNavBar)
    console.log('ddd ==> select', key)
  }, [])

  // 标题栏组件
  const titleComponent = useMemo(() => {
    const navBarProps = {
      deviceTypes,
      dimensions,
      activeNavBar,
      setActiveNavBar: handleNavBarChange,
    }

    return (
      <div
        className="h-32 w-full flex justify-between items-center pl-8"
        style={TITLE_STYLES.container}
      >
        <div className="flex items-center justify-center" style={TITLE_STYLES.title}>
          <img src={iconMap[productLineName]} alt={productLineName} style={TITLE_STYLES.icon} />
          <span>{productLineName}</span>
        </div>
        <NavLine {...navBarProps} />
      </div>
    )
  }, [productLineName, deviceTypes, dimensions, activeNavBar, handleNavBarChange])

  // 内容参数
  const contentParams = useMemo<ContentParams>(
    () => ({
      activeNavBar,
      data: tab,
    }),
    [activeNavBar, tab]
  )

  return (
    <div className="rounded-6 mb-6 prd-boardline">
      {titleComponent}
      <div className="w-full bg-white rounded-6 px-8">{renderContent(contentParams)}</div>
    </div>
  )
}

BoardLine.displayName = 'BoardLine'

export default memo(BoardLine)
