import React, { memo } from 'react'
import styled from 'styled-components'
import { InfoCircleOutlined } from '@hi-ui/icons'
import Tooltip from '@hi-ui/tooltip'
import { TITLE_TEXTS } from '../monitor-constant'

// 定义组件 props 类型
type MonitorTitleProps = {
  icon: React.ReactNode
  title: string
  topPadding?: number // 仅保留 topPadding 控制
  tooltip?: string // 可选 tooltip 内容
  subtitle?: string // 可选副标题
}

// 图标容器样式（固定样式）
const IconWrapper = styled.span`
  display: inline-flex;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  flex-shrink: 0;

  & > * {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
  }

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
`

// 标题文本样式（固定样式）
const TitleText = styled.div`
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #1f2733;
  display: flex;
  align-items: center;
`

// 副标题样式
const SubtitleText = styled.span`
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #5a6c84;
  margin-left: 12px;
`

// Tooltip 图标样式
const TooltipIcon = styled.span`
  margin-left: 4px;
  display: inline-flex;
  align-items: center;
  color: #8a94a6;
  cursor: pointer;
`

// 外层容器样式（仅接受 topPadding）
const TitleWrapper = styled.div<{ topPadding?: number }>`
  display: flex;
  align-items: center;
  margin: 0 !important;
  padding: ${(props) => props.topPadding ?? 32}px 0 12px 0;
`

// 组件实现
const MonitorTitle: React.FC<MonitorTitleProps> = memo(
  ({ icon, title, topPadding, tooltip, subtitle }) => {
    return (
      <TitleWrapper topPadding={topPadding}>
        <IconWrapper>{icon}</IconWrapper>
        <TitleText>
          {title}
          {tooltip && (
            <Tooltip
              title={
                tooltip?.includes('\n')
                  ? tooltip.split('\n').map((line, i) => (
                      <React.Fragment key={i}>
                        {line}
                        {i < tooltip.split('\n').length - 1 && <br />}
                      </React.Fragment>
                    ))
                  : tooltip || TITLE_TEXTS.DEFAULT_TOOLTIP
              }
              placement="top"
            >
              <TooltipIcon>
                <InfoCircleOutlined size={16} />
              </TooltipIcon>
            </Tooltip>
          )}
          {subtitle && !tooltip && <SubtitleText>{subtitle}</SubtitleText>}
        </TitleText>
      </TitleWrapper>
    )
  }
)

MonitorTitle.displayName = 'MonitorTitle'
export default MonitorTitle
