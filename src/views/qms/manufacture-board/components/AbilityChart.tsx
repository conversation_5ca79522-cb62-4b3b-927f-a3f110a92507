/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useCallback, useEffect, useRef } from 'react'
import * as echarts from 'echarts'

import { ABILITY_CHART_COLORS, ECHART_COLORS, PERSON_CHART_TEXTS } from '../monitor-constant'

interface ChartData {
  days: string[]
  evaporationData: number[]
  precipitationData: number[]
  temperatureData: number[]
}

interface ChartTexts {
  gradeA: string
  gradeB: string
  missRate: string
  gradeVolume: string
}

interface AbilityChartProps {
  chartData: ChartData
  chartTexts?: Partial<ChartTexts>
}

const AbilityChart = memo<AbilityChartProps>(({ chartData, chartTexts = {} }) => {
  const {
    gradeA = PERSON_CHART_TEXTS.GRADE_A,
    gradeB = PERSON_CHART_TEXTS.GRADE_B,
    missRate = PERSON_CHART_TEXTS.MISS_RATE,
    gradeVolume = PERSON_CHART_TEXTS.GRADE_VOLUME,
  } = chartTexts
  const { days, evaporationData, precipitationData, temperatureData } = chartData
  const chartRef = useRef<HTMLDivElement>(null)
  const chart = useRef<echarts.ECharts | null>(null)

  const createChart = useCallback(() => {
    if (!chartRef.current) return

    // 清理现有图表实例
    if (chart.current) {
      chart.current.dispose()
      chart.current = null
    }

    // 初始化新图表实例
    const chartInstance = echarts.init(chartRef.current)
    chart.current = chartInstance

    // 图表配置
    const option: echarts.EChartsOption = {
      title: {
        text: `{mainTitle|${PERSON_CHART_TEXTS.MAIN_TITLE}} {subTitle|${PERSON_CHART_TEXTS.SUB_TITLE}}`,
        left: 20,
        top: 20,
        textStyle: {
          rich: {
            mainTitle: {
              fontWeight: 'bold',
              fontSize: 16,
              color: '#1F2733',
              padding: [0, 12, 0, 0],
            },
            subTitle: {
              fontSize: 14,
              color: '#9299A6',
              fontWeight: 'normal',
            },
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`
          params.forEach((item: any) => {
            const valueLabel =
              item.seriesName === missRate ? `${item.value} ${missRate}` : `${item.value}`
            result += `<div style="margin-bottom: 10px;list-style:none;">
              <span style="background-color:${item.color};" class="g2-tooltip-marker"></span>
              ${item.seriesName}: ${valueLabel}
            </div>`
          })
          return result
        },
      },
      legend: {
        data: [gradeA, gradeB, missRate],
        right: 16,
        top: 20,
        itemGap: 20,
        selectedMode: true,
        textStyle: {
          fontSize: 12,
          color: ECHART_COLORS.legendText,
        },
        icon: 'roundRect',
        itemWidth: 10,
        itemHeight: 6,
        itemStyle: {
          borderRadius: 1,
        },
      },
      grid: {
        top: 120,
        left: 25,
        right: 40,
        bottom: 20,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: days,
        axisPointer: {
          type: 'shadow',
        },
        axisLine: {
          lineStyle: {
            color: ECHART_COLORS.splitLine,
            width: 1,
          },
        },
        axisTick: {
          lineStyle: {
            color: ECHART_COLORS.splitLine,
            width: 1,
          },
        },
        axisLabel: {
          color: ECHART_COLORS.axis,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: gradeVolume,
          nameLocation: 'end',
          nameGap: 25,
          nameTextStyle: {
            padding: [0, -8, 0, 0],
            align: 'right',
            verticalAlign: 'bottom',
            fontSize: 12,
            fontWeight: 400,
            color: ECHART_COLORS.axis,
          },
          min: 0,
          max: 250,
          interval: 50,
          axisLabel: {
            formatter: '{value} ',
            margin: 15,
            color: ECHART_COLORS.axis,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: ECHART_COLORS.splitLine,
              width: 1,
            },
          },
        },
        {
          type: 'value',
          name: missRate,
          nameLocation: 'end',
          nameGap: 25,
          nameTextStyle: {
            padding: [0, 0, 0, 12],
            align: 'left',
            verticalAlign: 'bottom',
            fontSize: 12,
            fontWeight: 400,
            color: ECHART_COLORS.axis,
          },
          min: 0,
          max: 25,
          interval: 5,
          axisLabel: {
            formatter: '{value}',
            margin: 15,
            color: ECHART_COLORS.axis,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: ECHART_COLORS.splitLine,
              width: 1,
            },
          },
        },
      ],
      series: [
        {
          name: gradeA,
          type: 'bar',
          stack: 'total',
          barWidth: 24,
          itemStyle: {
            color: ABILITY_CHART_COLORS.GRADE_A,
            borderRadius: [0, 0, 0, 0],
            borderWidth: 1,
            borderColor: '#fff',
          },
          data: evaporationData,
        },
        {
          name: gradeB,
          type: 'bar',
          stack: 'total',
          barWidth: 24,
          itemStyle: {
            color: ABILITY_CHART_COLORS.GRADE_B,
            borderRadius: [90, 90, 0, 0],
            borderWidth: 1,
            borderColor: '#fff',
          },
          data: precipitationData,
        },
        {
          name: missRate,
          type: 'line',
          yAxisIndex: 1,
          lineStyle: {
            color: ABILITY_CHART_COLORS.MISS_RATE,
            type: 'dashed',
            width: 2,
            dashOffset: 5,
          },
          itemStyle: {
            color: ABILITY_CHART_COLORS.MISS_RATE,
          },
          symbol: 'circle',
          symbolSize: 8,
          data: temperatureData,
        },
      ],
    }

    chart.current.setOption(option)

    const handleResize = () => {
      chart.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chart.current?.dispose()
      chart.current = null
    }
  }, [
    days,
    evaporationData,
    gradeA,
    gradeB,
    gradeVolume,
    missRate,
    precipitationData,
    temperatureData,
  ])

  useEffect(() => {
    const cleanup = createChart()

    // 窗口大小变化时重新调整图表大小
    const handleResize = () => {
      chart.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      cleanup?.()
      window.removeEventListener('resize', handleResize)
    }
  }, [createChart])

  return (
    <div
      ref={chartRef}
      style={{
        height: '100%',
        width: '100%',
        minHeight: '400px', // 确保图表有最小高度
      }}
    />
  )
})

AbilityChart.displayName = 'AbilityChart'
export default AbilityChart
