import React, { useCallback, useMemo } from 'react'
import styled from 'styled-components'
import Tooltip from '@hi-ui/tooltip'
import Select from '@hi-ui/select'
import { InfoCircleOutlined } from '@hi-ui/icons'
import { BOARD_LINE_STYLE, DIMENSION_TYPES } from '../constant'
import type { NavBarProps } from '../interface'
import { defaultTitleSvg } from '@/views/qms/billboard/components/NavLine'
import { Divider, NavButton } from './Widgets'

const NavBarContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex: 1;
`

// 常量配置
const DEVICE_ICON_SIZE = {
  width: '16px',
  height: '16px',
}

const SELECT_CONFIG = {
  width: '76px',
  placeholder: '更多',
  optionWidth: 160,
  appearance: 'line' as const,
}

const TOOLTIP_CONFIG = {
  trigger: 'hover' as const,
  iconStyle: {
    fontSize: 16,
    marginLeft: 4,
    color: '#5F6A7A',
  },
}

// 分割线配置
const DIVIDER_CONFIG = {
  withMore: '0 12px',
  withoutMore: '0 12px 0 6px',
}

const NavLine: React.FC<NavBarProps> = ({
  deviceTypes,
  dimensions,
  activeNavBar,
  setActiveNavBar,
}) => {
  const dimensionsLength = dimensions.length
  const lastIndex = dimensionsLength - 1

  // 处理导航栏点击事件
  const handleNavBarClick = useCallback(
    (key, value) => {
      setActiveNavBar({ ...activeNavBar, [key]: value }, key)
    },
    [activeNavBar, setActiveNavBar]
  )

  // 渲染设备类型按钮
  const renderDeviceTypeButtons = useMemo(() => {
    if (!Array.isArray(deviceTypes) || deviceTypes.length <= 1) {
      return null
    }

    return (
      <div className="flex justify-start items-center">
        {deviceTypes.map((type, index) => (
          <NavButton
            key={`device-${type}-${index}`}
            active={activeNavBar.deviceType === type}
            onClick={() => handleNavBarClick(DIMENSION_TYPES.PHONE.key, type)}
          >
            {defaultTitleSvg[type]?.(DEVICE_ICON_SIZE)}
            <span className="ml-3">{type}</span>
          </NavButton>
        ))}
      </div>
    )
  }, [deviceTypes, activeNavBar.deviceType, handleNavBarClick])

  // 渲染维度选择器
  const renderDimensionSelectors = useMemo(() => {
    return dimensions.map((item, index) => {
      const list = item.list || []
      if (list.length === 0) return null

      // 渲染控制数量
      const limit = item.limit || list.length
      const displayList = list.slice(0, limit)
      const moreList = list.slice(limit).map((item) => ({ title: item, id: item }))
      const isMoreActive = moreList.some((s) => s.id === activeNavBar[item.key])

      return (
        <div key={item.key} className="flex justify-end items-center">
          {/* 维度标签 */}
          {dimensionsLength > 1 && (
            <span
              className="mr-3 flex items-center"
              style={{
                fontSize: BOARD_LINE_STYLE.LABEL_FONT_SIZE,
                color: BOARD_LINE_STYLE.LABEL_COLOR,
              }}
            >
              {item.title}
              {item?.tooltip && (
                <Tooltip title={item.tooltip} trigger={TOOLTIP_CONFIG.trigger}>
                  <InfoCircleOutlined style={TOOLTIP_CONFIG.iconStyle} />
                </Tooltip>
              )}
            </span>
          )}

          {/* 显示的按钮 */}
          {displayList.map((option) => (
            <NavButton
              key={option}
              active={activeNavBar[item.key] === option}
              onClick={() => handleNavBarClick(item.key, option)}
            >
              {option}
            </NavButton>
          ))}

          {/* 更多选项下拉框 */}
          {moreList.length > 0 && (
            <Select
              className={`prd-select-more ${isMoreActive ? 'active' : ''}`}
              data={moreList}
              value={activeNavBar[item.key]}
              clearable={false}
              searchable
              appearance={SELECT_CONFIG.appearance}
              style={{ width: SELECT_CONFIG.width }}
              placeholder={SELECT_CONFIG.placeholder}
              optionWidth={SELECT_CONFIG.optionWidth}
              onChange={(value) => {
                handleNavBarClick(item.key, value)
              }}
            />
          )}

          {/* 分割线 */}
          {index !== lastIndex && (
            <Divider
              margin={moreList.length > 0 ? DIVIDER_CONFIG.withMore : DIVIDER_CONFIG.withoutMore}
            />
          )}
        </div>
      )
    })
  }, [dimensions, activeNavBar, dimensionsLength, lastIndex, handleNavBarClick])

  return (
    <NavBarContainer>
      {renderDeviceTypeButtons}
      <div className="flex">{renderDimensionSelectors}</div>
    </NavBarContainer>
  )
}

export default NavLine
