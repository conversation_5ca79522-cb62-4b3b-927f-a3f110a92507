/* eslint-disable react-hooks/exhaustive-deps */
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  memo,
  useCallback,
} from 'react'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'
import { cloneDeep } from 'lodash'
import getEchartsOption from '../hooks/useSetOption'
import { BOARD_CONTAINER_ID, CHART_TYPE } from '../constant'
import type {
  PieChartData,
  BarChartData,
  LineChartData,
  FfrChartData,
  AppTheme,
} from '../interface'

import type { GraphicElement } from '@/views/qms/billboard/interface'
import { getTargetElementId } from '@/views/qms/billboard/utils'
import {
  CHART_ID_SEPARATOR,
  CHART_DEFAULT_Z_INDEX,
  ERROR_MESSAGES,
  LOADING_TEXT,
  CHART_INTERACTION,
  THEME_COLORS,
} from '@/views/qms/billboard/constant'

// 明确 ref 暴露的方法类型
type ChartRefMethods = {
  chartInstance: ECharts | null // 实例类型
}

interface ChartProps {
  width?: string | number
  height?: string | number
  className?: string
  ref?: React.ForwardedRef<ChartRefMethods>
  controlXText?: number[]
  data: PieChartData | BarChartData | LineChartData | FfrChartData
  config: {
    chartType: string
    theme?: AppTheme
    extraLine?: { key: string; text: string }[]
  }
  onClick?: (params: AnyType) => void
  lastPeriod?: string
}

const ChartComponent: React.FC<ChartProps> = forwardRef((props, ref) => {
  const {
    width = '100%',
    height = '260px',
    className = '',
    controlXText = [],
    data,
    config,
    onClick,
    lastPeriod,
  } = props

  const chartRef = useRef<HTMLDivElement>(null)
  const zIndexRef = useRef(CHART_DEFAULT_Z_INDEX)
  const optionsRef = useRef<EChartsOption>({})
  const graphicRef = useRef<GraphicElement[]>([])
  const [chartInstance, setChartInstance] = useState<ECharts | null>(null) // 实例状态管理
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedParams, setSelectedParams] = useState<AnyType>(null)
  const handleClickRef = useRef<((params: AnyType) => void) | null>(null)

  useEffect(() => {
    let returnBack
    try {
      const newChartInstance = echarts.init(chartRef.current, undefined, {
        width: typeof width === 'number' ? width : undefined,
        height: typeof height === 'number' ? height : undefined,
      })

      setChartInstance(newChartInstance)
      setIsLoading(false)

      returnBack = () => {
        newChartInstance.dispose()
        setChartInstance(null)
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(`${ERROR_MESSAGES.CHART_INIT_FAILED}${err.message}`)
      } else {
        setError(`${ERROR_MESSAGES.CHART_INIT_FAILED}${ERROR_MESSAGES.UNKNOWN_ERROR}`)
      }
      setIsLoading(false)

      returnBack = () => {
        setChartInstance(null)
      }
    }

    return returnBack
  }, [])

  const initChart = useCallback(() => {
    if (!chartInstance) return
    chartInstance.clear()
    zIndexRef.current = CHART_DEFAULT_Z_INDEX

    // 监听图表元素的鼠标悬停事件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    chartInstance.on(CHART_INTERACTION.MOUSEOVER_EVENT, function (params: any) {
      // 只处理图形元素的事件
      if (params.componentType !== CHART_INTERACTION.COMPONENT_TYPE_GRAPHIC) return

      // 获取目标元素ID
      const targetId = getTargetElementId(params.event)
      if (!targetId) return

      // 从ID中提取前缀文本（用于匹配同组元素）
      const preText = targetId.split?.(CHART_ID_SEPARATOR)?.[0]
      if (!preText) return

      // 更新图形元素的Z轴索引，使当前悬停的元素组显示在最上层
      const zIndex = zIndexRef.current++
      const newGraphic = cloneDeep(graphicRef.current).map((item) => {
        // 只更新同组元素（具有相同前缀的元素）
        if (item.id && item.id.startsWith(preText)) {
          item.z = zIndex
        }
        return item
      })

      // 应用新的配置
      graphicRef.current = newGraphic
      chartInstance.setOption({
        ...optionsRef.current,
        graphic: newGraphic,
      })
    })
    const handleClick = (params: AnyType) => {
      if (config.chartType === CHART_TYPE.GENERAL) {
        if (params.componentType !== 'series') {
          chartInstance?.dispatchAction({ type: 'downplay' })
          setSelectedParams(null)
          return
        }
        setSelectedParams(params)
        chartInstance?.dispatchAction({
          type: 'highlight',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })
        onClick?.(params)
      }
    }
    if (handleClickRef.current) {
      chartInstance.off('click', handleClickRef.current)
    }
    handleClickRef.current = handleClick
    chartInstance.on('click', handleClickRef.current)
    try {
      const { option, graphic: getGraphic } = getEchartsOption({
        data,
        config,
        controlXText,
      })
      optionsRef.current = option
      chartInstance.setOption({
        ...option,
        graphic: undefined,
      })

      if (
        (
          config as {
            extraLine?: {
              length: number
            }
          }
        ).extraLine?.length
      ) {
        setTimeout(() => {
          const mergegraphic = getGraphic(chartInstance, { data, config })
          const newGraphic = cloneDeep(mergegraphic)
          graphicRef.current = newGraphic
          chartInstance.setOption({
            ...option,
            graphic: newGraphic,
          })
        }, 300)
      }
    } catch (err: Error | unknown) {
      if (err instanceof Error) {
        setError(`${ERROR_MESSAGES.CHART_OPTION_FAILED}${err.message}`)
      } else {
        setError(`${ERROR_MESSAGES.CHART_OPTION_FAILED}${ERROR_MESSAGES.UNKNOWN_ERROR}`)
      }
    }
  }, [chartInstance, data, config, controlXText, setSelectedParams])

  useEffect(() => {
    initChart()
  }, [data, chartInstance])

  useEffect(() => {
    const resizeChart = () => {
      chartInstance?.resize({
        width: typeof width === 'string' ? undefined : width,
        height: typeof height === 'string' ? undefined : height,
      })
    }
    const parentDom = document.getElementById(BOARD_CONTAINER_ID)
    let resizeObserver: ResizeObserver | null = null

    if (parentDom) {
      resizeObserver = new ResizeObserver(resizeChart)
      resizeObserver.observe(parentDom)
    }

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect()
      }
      if (handleClickRef.current && chartInstance) {
        chartInstance.off('click', handleClickRef.current)
      }
    }
  }, [width, height, chartInstance])
  useEffect(() => {
    if (config.chartType === CHART_TYPE.GENERAL && lastPeriod) {
      setTimeout(() => {
        setSelectedParams({
          seriesIndex: 0,
          dataIndex: Number(lastPeriod.slice(lastPeriod.length - 2)) - 1,
          color: THEME_COLORS.ORANGE.CHART_COLORS[0],
        })
      }, 300)
    }
  }, [lastPeriod, config, data])
  useEffect(() => {
    if (chartInstance && selectedParams) {
      const pointInPixel = chartInstance.convertToPixel(
        { seriesIndex: selectedParams.seriesIndex },
        [selectedParams.dataIndex]
      )
      const graphic = [
        {
          type: 'line',
          z: 100,
          id: 'hadleClickLine',
          shape: {
            x1: pointInPixel[0],
            y1: 40,
            x2: pointInPixel[0],
            y2: chartInstance.getHeight() - 24,
          },
          style: {
            stroke: selectedParams.color,
            lineWidth: 1,
          },
        },
      ]
      chartInstance.setOption({ graphic })
    }
  }, [selectedParams, chartInstance])
  useImperativeHandle(ref, () => ({
    chartInstance,
  }))

  if (error) {
    return <div className={`${className} text-red-500`}>{error}</div>
  }

  return (
    <div
      className={`relative ${className} w-full h-full`}
      style={{
        width: typeof width === 'string' ? width : '100%',
        height: typeof height === 'string' ? height : '100%',
      }}
    >
      {isLoading && (
        <div className="absolute inset-0 bg-white flex items-center justify-center">
          <div className="spinner-border text-blue-500" role="status">
            <span className="visually-hidden">{LOADING_TEXT}</span>
          </div>
        </div>
      )}
      <div ref={chartRef} className="w-full h-full" />
    </div>
  )
})

ChartComponent.displayName = 'ChartComponent'
export default memo(ChartComponent)
