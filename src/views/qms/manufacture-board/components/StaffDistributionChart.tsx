/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import { STAFF_CHART_COLORS } from '../monitor-constant'
import { getPieConfig } from '../echarts/monitor'
import { getStaffPieData } from '../mock-data'

// 明确 ref 暴露的方法类型
type ChartRefMethods = {
  chartInstance: ECharts | null
}

interface StaffDistributionChartProps {
  width?: string | number
  height?: string | number
  className?: string
}

const ERROR_MESSAGES = {
  CHART_INIT_FAILED: '图表初始化失败: ',
  CHART_OPTION_FAILED: '图表配置失败: ',
  UNKNOWN_ERROR: '未知错误',
}

const LOADING_TEXT = '加载中...'

const StaffDistributionChart = forwardRef<ChartRefMethods, StaffDistributionChartProps>(
  (props, ref) => {
    const { width = '100%', height = '100%', className = '' } = props

    const chartRef = useRef<HTMLDivElement>(null)
    const [chartInstance, setChartInstance] = useState<ECharts | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // 准备环状图数据
    const preparePieData = useCallback(() => getStaffPieData(), [])

    // 初始化图表实例
    useEffect(() => {
      let cleanup: (() => void) | undefined

      try {
        if (!chartRef.current) return

        const newChartInstance = echarts.init(chartRef.current, undefined, {
          width: typeof width === 'number' ? width : undefined,
          height: typeof height === 'number' ? height : undefined,
        })

        setChartInstance(newChartInstance)
        setIsLoading(false)

        cleanup = () => {
          newChartInstance.dispose()
          setChartInstance(null)
        }
      } catch (err) {
        if (err instanceof Error) {
          setError(`${ERROR_MESSAGES.CHART_INIT_FAILED}${err.message}`)
        } else {
          setError(`${ERROR_MESSAGES.CHART_INIT_FAILED}${ERROR_MESSAGES.UNKNOWN_ERROR}`)
        }
        setIsLoading(false)
      }

      return cleanup
    }, [width, height])

    // 创建环状图
    const createPieChart = useCallback(() => {
      if (!chartInstance) return

      try {
        const data = preparePieData()
        const option = getPieConfig(data, STAFF_CHART_COLORS)

        // 设置图表配置并渲染
        chartInstance.setOption(option)
      } catch (err) {
        if (err instanceof Error) {
          setError(`${ERROR_MESSAGES.CHART_OPTION_FAILED}${err.message}`)
        } else {
          setError(`${ERROR_MESSAGES.CHART_OPTION_FAILED}${ERROR_MESSAGES.UNKNOWN_ERROR}`)
        }
      }
    }, [chartInstance, preparePieData])

    // 当图表实例创建后，设置图表配置
    useEffect(() => {
      if (chartInstance) {
        createPieChart()
      }
    }, [chartInstance, createPieChart])

    // 响应窗口大小变化
    useEffect(() => {
      const handleResize = () => {
        chartInstance?.resize({
          width: typeof width === 'string' ? undefined : width,
          height: typeof height === 'string' ? undefined : height,
        })
      }

      // 使用 ResizeObserver 监听容器大小变化
      let resizeObserver: ResizeObserver | null = null
      if (chartRef.current) {
        resizeObserver = new ResizeObserver(handleResize)
        resizeObserver.observe(chartRef.current)
      }

      // 同时监听窗口大小变化
      window.addEventListener('resize', handleResize)

      return () => {
        if (resizeObserver) {
          resizeObserver.disconnect()
        }
        window.removeEventListener('resize', handleResize)
      }
    }, [chartInstance, width, height])

    // 暴露图表实例
    useImperativeHandle(ref, () => ({
      chartInstance,
    }))

    if (error) {
      return <div className={`${className} text-red-500`}>{error}</div>
    }

    return (
      <div
        className={`relative ${className}`}
        style={{
          width: typeof width === 'string' ? width : '100%',
          height: typeof height === 'string' ? height : '100%',
        }}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="spinner-border text-blue-500" role="status">
              <span className="visually-hidden">{LOADING_TEXT}</span>
            </div>
          </div>
        )}
        <div ref={chartRef} className="w-full h-full" />
      </div>
    )
  }
)

StaffDistributionChart.displayName = 'StaffDistributionChart'
export default memo(StaffDistributionChart)
