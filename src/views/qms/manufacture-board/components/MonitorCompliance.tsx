import React, { FC, memo } from 'react'
import { downIcon, ENVIRONMENT_MODULE_CONSTANTS, RANKING_COLORS } from '../monitor-constant'
import '../monitor.scss'

const TREND_ICON_SIZE = 20 // 定义趋势图标尺寸常量

// 排名项的属性接口
interface RankingItemProps {
  index: number
  name: string
  value: number
  trend: 'up' | 'down' | 'stable'
}

// 根据索引值确定背景色
const getIndexBackgroundColor = (index: number) => {
  switch (index) {
    case 1:
      return RANKING_COLORS.FIRST
    case 2:
      return RANKING_COLORS.SECOND
    default:
      return RANKING_COLORS.DEFAULT
  }
}

// 排名项组件
interface RankingItemProps {
  index: number
  name: string
  value: number
  trend: 'up' | 'down' | 'stable'
  showTrend?: boolean
}

const RankingItem: FC<RankingItemProps> = memo(
  ({ index, name, value, trend, showTrend = true }) => {
    const renderTrendIcon = () => {
      switch (trend) {
        case 'up':
          return (
            <img
              src={downIcon}
              alt="up"
              className="ranking-item__trend-icon ranking-item__trend-icon--up"
              style={{ width: TREND_ICON_SIZE, height: TREND_ICON_SIZE }}
            />
          )
        case 'down':
          return (
            <img
              src={downIcon}
              alt="down"
              className="ranking-item__trend-icon ranking-item__trend-icon--down"
              style={{
                width: TREND_ICON_SIZE,
                height: TREND_ICON_SIZE,
                transform: 'rotate(180deg)',
              }}
            />
          )
        default:
          return (
            <span
              className="ranking-item__trend-icon ranking-item__trend-icon--stable"
              style={{
                width: TREND_ICON_SIZE,
                height: TREND_ICON_SIZE,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              -
            </span>
          )
      }
    }

    return (
      <div className={`ranking-item ${!showTrend ? 'ranking-item--no-trend' : ''}`}>
        <div
          className="ranking-item__index"
          style={{ backgroundColor: getIndexBackgroundColor(index) }}
        >
          {index}
        </div>
        <div className="ranking-item__name">{name}</div>
        <div className="ranking-item__value">{value}%</div>
        {showTrend && <div className="ranking-item__trend">{renderTrendIcon()}</div>}
      </div>
    )
  }
)

RankingItem.displayName = 'RankingItem'

// 排名数据接口
interface RankingData {
  index: number
  name: string
  value: number
  trend: 'up' | 'down' | 'stable'
}

// 工厂详情数据接口
interface FactoryDetail {
  factory: string
  value: string
  target?: string
}

// 环境数据项接口
interface EnvironmentItem {
  name: string
  value: string
  target: string
  details?: FactoryDetail[]
}

// 内部使用的模块数据接口（合并常量和变量数据）
interface ComplianceModuleData {
  title: string
  icon: string
  description: string
  value: number
  standardRange: string
  rankings: RankingData[]
}

interface MonitorComplianceProps {
  className?: string
  environment?: EnvironmentItem[]
}

// 符合率监控组件
const MonitorCompliance: FC<MonitorComplianceProps> = memo(
  ({ className = '', environment = [] }) => {
    // 将环境数据转换为组件内部使用的格式
    const modulesData: ComplianceModuleData[] = ENVIRONMENT_MODULE_CONSTANTS.map((constant) => {
      // 查找对应的环境数据项
      const envItem = environment.find((item) => item.name === constant.title) || {
        value: '0',
        target: '≥ 95%',
        details: [],
      }

      // 将工厂详情转换为排名数据
      const rankings: RankingData[] = (envItem.details || []).map((detail, idx) => ({
        index: idx + 1,
        name: detail.factory,
        value: parseFloat(detail.value),
        trend: 'stable', // 默认为稳定，可以根据实际需求修改
      }))

      return {
        title: constant.title,
        icon: constant.icon,
        description: constant.description,
        value: parseFloat(envItem.value) || 0,
        standardRange: envItem.target || '100%',
        rankings,
      }
    })

    return (
      <div className={`monitor-compliance-container ${className}`}>
        <div className="compliance-modules">
          {modulesData.map((moduleData, index) => (
            <div key={index} className="compliance-module-container">
              {/* 标题栏 */}
              <div className="compliance-header">
                <div className="compliance-header__left">
                  <div className="compliance-header__icon">
                    <img
                      className="compliance-header__icon-circle"
                      src={moduleData.icon}
                      alt="up"
                    />
                  </div>
                  <div className="compliance-header__actual">
                    <div className="compliance-header__title">{moduleData.title}</div>
                    <div
                      className={`compliance-header__value ${
                        moduleData.value >=
                        parseFloat(moduleData.standardRange.replace(/[^\d.]/g, ''))
                          ? 'compliance-header__value--met'
                          : 'compliance-header__value--unmet'
                      }`}
                    >
                      {moduleData.value}%
                    </div>
                  </div>
                </div>
                <div className="compliance-header__target">
                  标准范围：{moduleData.standardRange}
                </div>
              </div>

              {/* 内容栏 */}
              <div className="compliance-content">
                {/* 排名信息 - 只有当有排名数据时才显示 */}
                {moduleData.rankings.length > 0 && (
                  <>
                    <div className="compliance-content__header">
                      <div className="compliance-content__title">未达标工厂</div>
                      {index !== modulesData.length - 1 && (
                        <div className="compliance-content__standard">
                          标准范围：{moduleData.standardRange}
                        </div>
                      )}
                    </div>
                    <div className="compliance-content__rankings">
                      {moduleData.rankings.map((item) => (
                        <RankingItem
                          key={item.index}
                          index={item.index}
                          name={item.name}
                          value={item.value}
                          trend={item.trend}
                          showTrend={index !== modulesData.length - 1}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }
)

MonitorCompliance.displayName = 'MonitorCompliance'

export default MonitorCompliance
