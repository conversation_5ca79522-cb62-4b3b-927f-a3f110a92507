import React, { useState, useCallback, useMemo, useEffect } from 'react'
import styled from 'styled-components'
import { RadioGroup, RadioGroupProps } from '@hi-ui/radio'
import Select, { SelectMergedItem } from '@hi-ui/select'
import {
  DATE_TYPE,
  DAY_TEXT,
  WEEK_TEXT,
  MONTH_TEXT,
  CSS_CLASSES,
} from '@/views/qms/billboard/constant'
import { KeyProject } from '@/views/qms/billboard/interface'
import { getLimitKeyProject } from '@/views/qms/billboard/config'
import { getFfrProjects } from '@/api/qms/manufacture-board/quality'

import { CardRadius, Divider } from './Widgets'
import type { ContentParams } from '../interface'
import FfrBoard from './FFR'
import useFfrBoardData from '../hooks/useFfrBoardData'
import { getLastMonth, getLastWeek, formatDateGeneral } from '../utils'

const FfrHeader = styled.div`
  padding-bottom: 12px;
  line-height: 32px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2733;
  border-bottom: 1.5px dashed #dfe2e8;
`

const generalData: RadioGroupProps['data'] = [
  {
    title: WEEK_TEXT,
    id: DATE_TYPE.WEEK,
  },
  {
    title: MONTH_TEXT,
    id: DATE_TYPE.MONTH,
  },
]
const keyProjectData: RadioGroupProps['data'] = [
  {
    title: DAY_TEXT,
    id: DATE_TYPE.DAY,
  },
  ...generalData,
]

const getDateGeneral = (marketDate: string) => {
  return marketDate === DATE_TYPE.WEEK ? getLastWeek() : getLastMonth()
}

const QualityFfr: React.FC<ContentParams> = (props) => {
  const { activeNavBar } = props
  const [marketDate, setMarketDate] = useState<string>(DATE_TYPE.WEEK)
  const [keyProjectDate, setKeyProjectDate] = useState<string>(DATE_TYPE.DAY)
  const [keyProjectSelect, setKeyProjectSelect] = useState<KeyProject | undefined>(() => undefined)

  const dateGeneral = useMemo(() => {
    return getDateGeneral(marketDate)
  }, [marketDate])

  const fetchParams = useMemo(() => {
    return {
      productLineId: 1,
      countryName: activeNavBar.region,
      brandClassName: activeNavBar.deviceType,
      dateTypeGeneral: marketDate,
      dateTypeKeyProject: keyProjectDate,
      dateGeneral: formatDateGeneral(dateGeneral),
    }
  }, [activeNavBar, dateGeneral, marketDate, keyProjectDate])

  const { boardData } = useFfrBoardData(fetchParams)

  // 初始化-回显型号
  useEffect(() => {
    const projectCodes = (boardData[2]?.data || []) as KeyProject[]
    setKeyProjectSelect((projectCodes as KeyProject[])[0])
  }, [boardData])
  // 缓存项目数据
  const memoKeyProjectData = useMemo(() => {
    const projectCodes = (boardData[2]?.data || []) as KeyProject[]
    return (keyProjectSelect && [keyProjectSelect]) || projectCodes
  }, [boardData, keyProjectSelect])

  const getKeyProjectNav = useCallback(
    (data: KeyProject[]) => {
      const leftValue = keyProjectSelect?.projectCode
      const rightValue = keyProjectSelect?.lastProjectCode
      const leftData: SelectMergedItem[] = []
      const rightData: SelectMergedItem[] = []
      data.forEach((item) => {
        leftData.push({
          title: item.projectCode,
          id: item.projectCode,
          ...item,
          disabled: item.projectCode === rightValue,
        })
        rightData.push({
          title: item.projectCode,
          id: item.projectCode,
          ...item,
          disabled: item.projectCode === leftValue,
        })
      })
      const controlLength = (data) => {
        if (data && data.projectData && data.projectData.trendChartData) {
          const trendChartData = data.projectData.trendChartData
          if (trendChartData[0] && trendChartData[0].dataList) {
            const dataList = trendChartData[0].dataList
            while (dataList.length < getLimitKeyProject[keyProjectDate]) {
              dataList.push({
                fmNum: undefined,
                fzNum: undefined,
                rate: undefined,
                index: dataList.length + 1,
              })
            }
          }
          if (trendChartData[1] && trendChartData[1].dataList) {
            const dataList = trendChartData[1].dataList
            while (dataList.length < getLimitKeyProject[keyProjectDate]) {
              dataList.push({
                fmNum: undefined,
                fzNum: undefined,
                rate: undefined,
                index: dataList.length + 1,
              })
            }
          }
        }
        return data
      }
      const rightHandleChange = async (keyProjectLastCode) => {
        const res = await getFfrProjects({
          ...fetchParams,
          keyProjectCode: leftValue,
          keyProjectLastCode,
        })
        if (res && res.code === 0) {
          const data = res.data || {}
          if (data.projectData && data.projectData.trendChartData) {
            data.projectData.trendChartData = data.projectData.trendChartData.filter(
              (item) => item.name
            )
          }
          setKeyProjectSelect(controlLength(data))
        }
      }
      const leftHandleChange = async (keyProjectCode, item) => {
        const res = await getFfrProjects({
          ...fetchParams,
          keyProjectCode,
          keyProjectLastCode: item.lastProjectCode,
        })
        if (res && res.code === 0) {
          const data = res.data || {}
          if (data.projectData && data.projectData.trendChartData) {
            data.projectData.trendChartData = data.projectData.trendChartData.filter(
              (item) => item.name
            )
          }
          setKeyProjectSelect(controlLength(data))
        }
      }
      return (
        <>
          <Select
            style={{ width: 100 }}
            className={CSS_CLASSES.HEIGHT_24}
            clearable={false}
            searchable
            data={leftData}
            value={leftValue}
            onChange={leftHandleChange}
          />
          <span style={{ margin: '0 8px', fontSize: '12px' }}>VS</span>
          <Select
            style={{ width: 100 }}
            className={CSS_CLASSES.HEIGHT_24}
            clearable={false}
            searchable
            data={rightData}
            value={rightValue}
            onChange={rightHandleChange}
          />
        </>
      )
    },
    [keyProjectSelect, setKeyProjectSelect, fetchParams, keyProjectDate]
  )

  return (
    <div className="flex py-10 gap-8" style={{ height: '536px' }}>
      <CardRadius style={{ flex: 1 }}>
        <FfrHeader className="flex justify-between">
          <span>大盘</span>
          <RadioGroup
            className="prd-statistical-radio"
            value={marketDate}
            type="button"
            data={generalData}
            onChange={(value) => setMarketDate(value as string)}
          />
        </FfrHeader>
        <div className="flex-1 w-full h-full">
          {boardData.length > 0 && (
            <FfrBoard
              {...boardData[0]}
              billData={props.data}
              activeNavBar={props.activeNavBar}
              fetchParams={fetchParams}
              selectedDate={{
                marketDate,
                keyProjectDate,
              }}
            />
          )}
        </div>
      </CardRadius>
      <CardRadius style={{ flex: 2 }}>
        <FfrHeader className="flex justify-between">
          <span>项目</span>
          <div className="flex items-center">
            <div className="flex items-center title-date-select">
              {getKeyProjectNav((boardData[2]?.data || []) as KeyProject[])}
            </div>
            <Divider height="16px" margin="0 16px" />
            <RadioGroup
              className="prd-statistical-radio"
              value={keyProjectDate}
              type="button"
              data={keyProjectData}
              onChange={(value) => setKeyProjectDate(value as string)}
            />
          </div>
        </FfrHeader>
        <div className="flex-1 w-full">
          <div className="h-full flex gap-6">
            {boardData.length > 0 && (
              <FfrBoard
                {...boardData[2]}
                data={memoKeyProjectData}
                billData={props.data}
                activeNavBar={props.activeNavBar}
                fetchParams={fetchParams}
                selectedDate={{
                  marketDate,
                  keyProjectDate,
                }}
              />
            )}
            {boardData.length > 0 && (
              <FfrBoard
                {...boardData[1]}
                billData={props.data}
                activeNavBar={props.activeNavBar}
                fetchParams={fetchParams}
                selectedDate={{
                  marketDate,
                  keyProjectDate,
                }}
              />
            )}
          </div>
        </div>
      </CardRadius>
    </div>
  )
}

export default QualityFfr
