import React, { memo, useState, useMemo, useCallback } from 'react'
import styled from 'styled-components'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'

import MfgDi from './MFGDI'
import CardLine, { IconTooltip } from './CardLine'
import {
  CARD_NAME,
  CARD_STYLE,
  BOARD_TYPE,
  CHART_TYPE,
  FONT_WEIGHT_MEDIUM,
  FONT_SIZE_NORMAL,
  FONT_SIZE_TITLE,
} from '../constant'
import { CardRadius } from './Widgets'
import { NavButtonText } from './NavButton'
import type { ContentParams } from '../interface'
import BoardChart from './BoardChart'

import { blueTheme, greenTheme } from '../config'
import { EXTRA_LINE_CONFIG } from '@/views/qms/billboard/constant'

const TitleWapper = styled.div`
  display: flex;
  align-items: center;
  font-weight: ${FONT_WEIGHT_MEDIUM};
  font-size: ${FONT_SIZE_NORMAL};
  line-height: ${FONT_SIZE_TITLE};
  color: ${CARD_STYLE.TITLE_COLOR};
`

// FPY&PY 配置数据 - 使用 useMemo 缓存
const FPY_PY_CONFIG = [
  {
    name: 'FPY',
    key: BOARD_TYPE.FPY,
    tooltip: 'FPY(First Pass Yield) 生产一次良率，指一次性通过所有测试的良品数占总试产数的比例',
  },
  {
    name: 'PY',
    key: BOARD_TYPE.PY,
    tooltip:
      'PY(Pass Yield) 生产最终良率，也为最终通过率，指最终通过所有测试的良品数占总试产数的比例',
  },
  {
    name: '不良工站',
    key: BOARD_TYPE.BAD_STATION,
    tooltip: '',
    tabs: [BOARD_TYPE.FPY, BOARD_TYPE.PY],
  },
] as const

// OBA 配置数据 - 使用 useMemo 缓存
const OBA_CONFIG = [
  {
    name: '成品检验DPPM',
    key: BOARD_TYPE.DPPM,
    tooltip: [
      'OBA(Out Of Box Audit，开箱检验）是对包装好的产品进行开箱检验，检查产品及配件的外观、功能、包装等是否符合质量标准',
      'DPPM(Defective Pieces Per Million，每百万个产品中的不良数) 衡量抽检产品中不良产品的缺陷程度，DPPM=10^6*（CR*3+MA*1+MI*0.3）/抽检数量，CR、MA、MI分别表示不良产品缺陷严重等级为Critical、Major、Minor的产品数量',
      'OBA-DPPM：指的是开箱检验发现的不良产品缺陷程度',
    ],
  },
  {
    name: '成品检验LAR',
    key: BOARD_TYPE.LAR,
    tooltip: [
      'OBA(Out Of Box Audi）是对包装好的产品进行开箱检验，检查产品及配件的外观、功能、包装等是否符合质量标准',
      'LAR (Lot Acceptance Rate) 是检验批合格率，LAR=（合格批数/总检验批数) * 100%',
      'OBA-LAR：指的是开箱检验的批合格率',
    ],
  },
  {
    name: '不良现象',
    key: BOARD_TYPE.BAD_BEHAVIOR,
  },
] as const

// 看板配置 - 使用 useMemo 缓存
const BOARD_CONFIG = {
  [BOARD_TYPE.FPY]: {
    chartType: CHART_TYPE.FPY_LINE,
    theme: blueTheme,
    extraLine: EXTRA_LINE_CONFIG,
  },
  [BOARD_TYPE.PY]: {
    chartType: CHART_TYPE.FPY_LINE,
    theme: greenTheme,
    extraLine: EXTRA_LINE_CONFIG,
  },
  [BOARD_TYPE.BAD_STATION]: {
    chartType: CHART_TYPE.FPY_BAR,
  },
  [BOARD_TYPE.DPPM]: {
    chartType: CHART_TYPE.FPY_LINE,
  },
  [BOARD_TYPE.LAR]: {
    chartType: CHART_TYPE.FPY_LINE,
  },
  [BOARD_TYPE.BAD_BEHAVIOR]: {
    chartType: CHART_TYPE.FPY_BAR,
  },
} as const

// 模拟数据配置 - 使用 useMemo 缓存
const MOCK_DATA_CONFIG = [
  {
    name: CARD_NAME.FPY_PY,
    list: FPY_PY_CONFIG,
  },
  {
    name: CARD_NAME.OBA,
    list: OBA_CONFIG,
  },
] as const

// 组件卡片 - 使用 memo 优化性能
interface ComponentCardProps {
  data: {
    name: string
    key: string
    tooltip?: string | string[]
    tabs?: string[]
  }
}

const ComponentCard = memo<ComponentCardProps>(({ data: item }) => {
  const [modeType, setModeType] = useState<string>(BOARD_TYPE.FPY)

  // 使用 useCallback 优化事件处理函数
  const handleModeChange = useCallback((newMode: string) => {
    setModeType(newMode)
  }, [])

  // 使用 useMemo 缓存图表配置
  const chartConfig = useMemo(() => BOARD_CONFIG[item.key], [item.key])
  const isEmpty = false

  return (
    <CardRadius className="flex-1">
      <div className="flex justify-between">
        <TitleWapper>
          <span>{item.name}</span>
          {item.tooltip && <IconTooltip text={item.tooltip} />}
        </TitleWapper>
        {item.tabs && (
          <NavButtonText list={item.tabs} setActive={handleModeChange} active={modeType} />
        )}
      </div>

      <div style={{ height: '216px' }}>
        {isEmpty ? (
          <EmptyState
            className="h-full flex items-center justify-center"
            title={intl.get('暂无数据')}
            indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
          />
        ) : (
          <BoardChart
            data={{ chartData: { dataList: [] } }}
            config={chartConfig}
            controlXText={[]}
            width="100%"
            height="100%"
            lastPeriod=""
          />
        )}
      </div>
    </CardRadius>
  )
})
ComponentCard.displayName = 'ComponentCard'

// FPY 组件 - 使用 memo 和 useMemo 优化性能
const ComponentFpy = memo<ContentParams>((props) => {
  console.log('ddd ==> ComponentFpy', props)
  // 使用 useMemo 缓存配置数据，避免每次渲染都重新创建
  const mockData = useMemo(() => MOCK_DATA_CONFIG, [])

  return (
    <>
      {mockData.map((item) => (
        <div key={item.name} className="prd-boardcell">
          <CardLine title={item.name} />
          <div className="flex gap-8">
            {item.list.map((child) => (
              <ComponentCard key={child.key} data={child} />
            ))}
          </div>
        </div>
      ))}
    </>
  )
})
ComponentFpy.displayName = 'ComponentFpy'

const QualityMfgdi: React.FC<ContentParams> = function (props) {
  return (
    <>
      <MfgDi {...props} />
      <ComponentFpy {...props} />
    </>
  )
}

export default QualityMfgdi
