/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react'
import { InfoCircleOutlined } from '@hi-ui/icons'
import Tooltip from '@hi-ui/tooltip'
import {
  MetricsData,
  METRICS_TEXTS,
  RateIcon,
  ValidFileIcon,
  OrderIcon,
  TOOLTIP_TEXTS,
} from '../monitor-constant'

interface MetricItem {
  label: string
  value: number | string
  unit?: string
  hasTooltip?: boolean
  tooltipContent?: string
}

interface Props {
  metrics?: MetricItem[]
  metricsData?: MetricsData
  labels?: {
    complianceRate?: string
    complianceCount?: string
    totalCount?: string
  }
  formulaTooltip?: string
}

export const KeyMetrics = ({
  metrics,
  metricsData,
  labels = {
    complianceRate: METRICS_TEXTS.COMPLIANCE_RATE,
    complianceCount: METRICS_TEXTS.COMPLIANCE_COUNT,
    totalCount: METRICS_TEXTS.TOTAL_COUNT,
  },
  formulaTooltip = TOOLTIP_TEXTS.MACHINE_COMPLIANCE,
}: Props) => {
  const displayMetrics =
    metrics ||
    (metricsData
      ? [
          {
            label: labels.complianceRate,
            value: metricsData.complianceRate ?? 0,
            unit: '%',
            hasTooltip: true,
            tooltipContent: formulaTooltip,
          },
          {
            label: labels.complianceCount,
            value: metricsData.complianceCount ?? 0,
          },
          {
            label: labels.totalCount,
            value: metricsData.totalCount ?? 0,
          },
        ]
      : [])

  // 渲染单个指标项
  const renderMetricItem = (metric: any, index: number) => {
    const icons = [RateIcon, ValidFileIcon, OrderIcon]
    return (
      <div className="metric-item" key={index} style={{ position: 'relative' }}>
        <div className="metric-item__label">
          {metric.label}
          {metric.hasTooltip && (
            <Tooltip title={metric.tooltipContent} placement="top">
              <span className="metric-item__info-icon">
                <InfoCircleOutlined size={16} />
              </span>
            </Tooltip>
          )}
        </div>
        <div className="metric-item__value-container">
          <span className="metric-item__value">{metric.value}</span>
          {metric.unit && <span className="metric-item__unit">{metric.unit}</span>}
        </div>
        <img
          src={icons[index]}
          alt=""
          className="metric-item__icon"
          style={{
            width: '58px',
            height: '58px',
            position: 'absolute',
            right: '8px',
            bottom: '0',
          }}
        />
      </div>
    )
  }

  return <div className="monitor-card__metrics">{displayMetrics.map(renderMetricItem)}</div>
}

KeyMetrics.displayName = 'KeyMetrics'

export default KeyMetrics
