/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import CardLine, { CardLineProps } from './Card'
import BoardChart from '../BoardChart'
import { FfrChartData } from '../../interface'
import {
  CARD_STYLE,
  BOARD_TYPE,
  FONT_WEIGHT_MEDIUM,
  FONT_SIZE_NORMAL,
  FONT_SIZE_TITLE,
} from '../../constant'
import { boardConfig } from '../../config'
import { defaultShowIndex } from '@/views/qms/billboard/config'
import { ThemeProvider } from 'styled-components'
import { ActiveNavBar } from '@/views/qms/billboard/components/NavLine'
import {
  GeneralData,
  SpecialData,
  KeyProject,
  TrendYearData,
} from '@/views/qms/billboard/interface'
import '@/views/qms/billboard/index.scss'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import {
  DATE_TYPE,
  WEEK_TEXT,
  MONTH_TEXT,
  INDICATORS,
  COMPARISON_TYPES,
  PRODUCT_TYPES,
  REGION,
} from '@/views/qms/billboard/constant'
import { DealBoardDataParams } from '@/views/qms/billboard/hooks/useDealBoardData'

// 为了类型安全，创建一个工具函数来转换数据
function adaptToBoardChartData(data: any, boardType: string): FfrChartData {
  if (boardType === BOARD_TYPE.SPECIAL) {
    // 为SPECIAL类型提供默认值
    return {
      ffrRate: 0,
      ffrYoy: 0,
      fzNum: data.fzNum || 0,
      fzYoy: 0,
      fmNum: data.fmNum || 0,
      fmYoy: 0,
      trendChartData: [],
      barChartData: data.barChartData || [],
      ...data, // 保留原有数据
    }
  }

  // 对于其他类型，假设数据结构已符合要求
  return data as FfrChartData
}

interface FfrBoardProps {
  data: GeneralData | SpecialData | KeyProject[]
  activeNavBar: ActiveNavBar
  boardName: typeof BOARD_TYPE.GENERAL | typeof BOARD_TYPE.SPECIAL | typeof BOARD_TYPE.KEY_PROJECTS
  selectedDate: {
    marketDate: string
    keyProjectDate: string
  }
  isEmpty: boolean
  billData: any
  fetchParams: DealBoardDataParams
}

const getSpecial = () => null

const getGeneral = (data, billData, activeNavBar) => {
  const { ffrRate, ffrYoy, fmNum, fmYoy, fzNum, fzYoy } = data || {}
  const getSpecialLastName = () => {
    if ([PRODUCT_TYPES.TV].includes(billData.productLineName)) {
      if (
        [PRODUCT_TYPES.MONITOR, PRODUCT_TYPES.TV].includes(activeNavBar.deviceType) &&
        activeNavBar.region === REGION.CHINA
      ) {
        return INDICATORS.MAX_WARRANTY_COUNT
      }
    }
    if ([PRODUCT_TYPES.LAPTOP].includes(billData.productLineName)) {
      if (activeNavBar.region === REGION.CHINA) {
        return INDICATORS.MAX_WARRANTY_COUNT
      }
    }
    return INDICATORS.DEVICE_WARRANTY_COUNT
  }
  return [
    {
      name: INDICATORS.FFR,
      value: ffrRate,
      additional: [COMPARISON_TYPES.YEAR_ON_YEAR, ffrYoy],
    },
    {
      name: INDICATORS.WORK_ORDER_COUNT,
      value: fzNum,
      additional: [COMPARISON_TYPES.YEAR_ON_YEAR, fzYoy],
    },
    {
      name: getSpecialLastName(),
      value: fmNum,
      additional: [COMPARISON_TYPES.YEAR_ON_YEAR, fmYoy],
    },
  ]
}

const getKeyProject = (data, billData) => {
  const { ffrRate, ffrYoy, fmNum, fmYoy, fzNum, fzYoy } = data || {}

  const getKeyProjectLastName = () => {
    if (
      [PRODUCT_TYPES.WEARABLE, PRODUCT_TYPES.TV, PRODUCT_TYPES.LAPTOP].includes(
        billData.productLineName
      )
    ) {
      return INDICATORS.ACCUMULATED_DELIVERY_COUNT
    }
    return INDICATORS.ACCUMULATED_ACTIVATIONS
  }
  return [
    {
      name: INDICATORS.ACCUMULATED_FFR,
      value: ffrRate,
      additional: [COMPARISON_TYPES.PREVIOUS_GENERATION, ffrYoy],
    },
    {
      name: INDICATORS.ACCUMULATED_WORK_ORDER_COUNT,
      value: fzNum,
      additional: [COMPARISON_TYPES.PREVIOUS_GENERATION, fzYoy],
    },
    {
      name: getKeyProjectLastName(),
      value: fmNum,
      additional: [COMPARISON_TYPES.PREVIOUS_GENERATION, fmYoy],
    },
  ]
}

function BillBoard({
  data,
  boardName,
  selectedDate,
  isEmpty,
  billData,
  activeNavBar,
}: FfrBoardProps) {
  const [keyProjectSelect, setKeyProjectSelect] = useState<KeyProject | undefined>(() => {
    return boardName === BOARD_TYPE.KEY_PROJECTS ? data[0] : undefined
  })
  const [currentYearPeriod, setCurrentYearPeriod] = useState<{ year: string; period: number }>()
  const [lastPeriod, setLastPeriod] = useState<string>() // 上一周期
  const childRef = useRef<any>(null)

  useEffect(() => {
    if (boardName === BOARD_TYPE.KEY_PROJECTS) {
      setKeyProjectSelect(data ? (data as KeyProject[])[0] : undefined)
    }
  }, [boardName, data])

  const findData = useMemo(() => {
    setCurrentYearPeriod(undefined)
    return (boardName === BOARD_TYPE.KEY_PROJECTS ? keyProjectSelect?.projectData : data) as
      | GeneralData
      | SpecialData
      | KeyProject['projectData']
  }, [boardName, data, keyProjectSelect])

  const currentBoardConfig = useMemo(
    () => boardConfig[boardName] || boardConfig[BOARD_TYPE.GENERAL],
    [boardName]
  )
  const titleHandle = useMemo(() => {
    let title = currentBoardConfig.name
    if (boardName === BOARD_TYPE.GENERAL) {
      // 大盘周期
      const period = currentYearPeriod
        ? `${currentYearPeriod.year}-${
            currentYearPeriod.period < 10
              ? '0' + currentYearPeriod.period
              : currentYearPeriod.period
          }`
        : lastPeriod || ''

      if (period) {
        title = period + (selectedDate.marketDate === DATE_TYPE.WEEK ? WEEK_TEXT : MONTH_TEXT)
      }
    } else if (boardName === BOARD_TYPE.SPECIAL) {
      // 三级故障-若数据存在，渲染图表title
      if (((data || {}) as SpecialData).barChartData?.length) return
    }

    return (
      <div
        className="flex justify-between items-center pt-5 pb-5"
        style={{
          fontWeight: FONT_WEIGHT_MEDIUM,
          fontSize: FONT_SIZE_NORMAL,
          lineHeight: FONT_SIZE_TITLE,
          color: CARD_STYLE.SUBTITLE_COLOR,
        }}
      >
        {title}
      </div>
    )
  }, [currentBoardConfig, lastPeriod, currentYearPeriod, selectedDate])

  const CardLineData = useMemo(() => {
    let newFindData = findData as GeneralData | SpecialData | KeyProject['projectData']
    if (boardName === BOARD_TYPE.GENERAL) {
      const generalData = data as GeneralData
      let trendChart = generalData.trendChartData?.[0] || {}
      let itemGeneral = trendChart.dataList?.find((item) => item.isLastPeriod)
      if (itemGeneral) {
        setLastPeriod(
          `${trendChart.name}-${
            itemGeneral.index < 10 ? '0' + itemGeneral.index : itemGeneral.index
          }`
        )
      }
      if (currentYearPeriod) {
        trendChart = (generalData.trendChartData?.find(
          (item) => item.name === currentYearPeriod.year
        ) || {}) as TrendYearData
        itemGeneral = trendChart.dataList?.find((item) => item.index === currentYearPeriod.period)
      }
      newFindData = itemGeneral
        ? {
            ...generalData,
            fmNum: itemGeneral.fmNum,
            fzNum: itemGeneral.fzNum,
            ffrYoy: itemGeneral.ffrYoy,
            fzYoy: itemGeneral.fzYoy,
            fmYoy: itemGeneral.fmYoy,
            ffrRate: itemGeneral.rate,
          }
        : { ...generalData }
    }
    return (
      {
        [BOARD_TYPE.GENERAL]: getGeneral,
        [BOARD_TYPE.SPECIAL]: getSpecial,
        [BOARD_TYPE.KEY_PROJECTS]: getKeyProject,
      }[boardName](newFindData, billData, activeNavBar) || []
    )
  }, [boardName, data, findData, activeNavBar, setLastPeriod, currentYearPeriod])

  // 使用适配函数处理数据
  const chartData = useMemo(() => {
    return adaptToBoardChartData(findData, boardName)
  }, [findData, boardName])
  const handleClick = useCallback(
    (params) => {
      if (boardName === BOARD_TYPE.GENERAL) {
        setCurrentYearPeriod({ year: params.seriesName, period: Number(params.name) })
      }
    },
    [boardName, setCurrentYearPeriod]
  )
  return (
    <ThemeProvider theme={currentBoardConfig.theme}>
      <div className="flex-1 h-full overflow-hidden flex flex-col">
        {titleHandle}
        {CardLineData && <CardLine cardLineData={CardLineData as CardLineProps['cardLineData']} />}
        {isEmpty ? (
          <EmptyState
            className="empty-state flex items-center justify-center"
            style={{ flex: 1 }}
            title={intl.get('暂无数据')}
            indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
          />
        ) : (
          <BoardChart
            ref={childRef}
            data={chartData}
            config={currentBoardConfig}
            controlXText={
              defaultShowIndex[
                boardName === BOARD_TYPE.KEY_PROJECTS
                  ? selectedDate.keyProjectDate
                  : selectedDate.marketDate
              ]
            }
            width="100%"
            height="100%"
            onClick={handleClick}
            lastPeriod={lastPeriod}
          />
        )}
      </div>
    </ThemeProvider>
  )
}

export default memo(BillBoard)
