import React, { memo, useEffect, useMemo, useRef, useState } from 'react'
import styled, { css } from 'styled-components'
import { convertNumber, effectStr } from '@/views/qms/billboard/utils'
import { downIcon, upIcon } from '@/views/qms/billboard/assets'
import Tooltip from '@hi-ui/tooltip'
import './card.scss'
import {
  PLACEHOLDER_STR,
  UNDEFINED_TARGET_TEXT,
  INFINITY_SYMBOL,
  CARD_STYLE,
  FONT_SIZE_NORMAL,
  FONT_SIZE_MEDIUM,
  FONT_SIZE_LARGE,
  FONT_SIZE_TITLE,
  FONT_WEIGHT_MEDIUM,
  NUMBER_CONVERSION,
  SUBSTANDARD_PROJECT,
  TEXT_CALCULATION_FACTOR_DOUBLE,
  TEXT_CALCULATION_FACTOR_SINGLE,
  CARD_IMAGE_SIZE,
  CARD_MAX_WIDTH,
  LINE_HEIGHT,
  SHOW_LITTLE_ARR,
  TRANSFORM_ROTATE,
  INDICATORS,
} from '@/views/qms/billboard/constant'

import { FONT_SIZE_SMALL } from '../../constant'

const CardOuter = styled.div`
  width: 100%;
  font-size: ${CARD_STYLE.TITLE_FONT_SIZE};
  box-sizing: border-box;

  ${({ theme }) => {
    return css`
      background-image: ${theme.borderColor};
      padding: 0.8px;
      border-radius: ${CARD_STYLE.CARD_BORDER_RADIUS};
    `
  }}
`

const CardMask = styled.div`
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: ${CARD_STYLE.CARD_BORDER_RADIUS};
`

const CardContent = styled.div`
  width: 100%;
  height: 100%;
  padding: ${CARD_STYLE.CARD_PADDING};
  padding-bottom: 13px;
  background-color: #fff;

  display: flex;
  flex-direction: column;
  gap: ${CARD_STYLE.CARD_GAP};

  ${({ theme }) => {
    return css`
      background: ${theme.background};
      border-radius: ${CARD_STYLE.CARD_BORDER_RADIUS};
    `
  }}
`

const TitleContainer = styled.div`
  width: 100%;
  height: ${LINE_HEIGHT};
  line-height: ${LINE_HEIGHT};
  font-size: ${CARD_STYLE.TITLE_FONT_SIZE};
  color: ${(props) => props.theme.color};
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
`

interface CardProps {
  name?: string
  value?: number | string
  additional?: [string, string | number | undefined]
  lastCard: boolean
}

export const Card: React.FC<CardProps> = ({ name, value, additional, lastCard }) => {
  const [isOverflow, setIsOverflow] = useState(false)
  const textRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new ResizeObserver(() => {
      if (textRef.current && typeof value === 'string') {
        const { clientWidth } = textRef.current

        if (name === SUBSTANDARD_PROJECT) {
          setIsOverflow(value.length * TEXT_CALCULATION_FACTOR_DOUBLE > clientWidth * 2)
        } else {
          setIsOverflow(value.length * TEXT_CALCULATION_FACTOR_SINGLE > clientWidth)
        }
      }
    })

    if (textRef.current) {
      observer.observe(textRef.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [value, name])

  const memoValue = useMemo(() => {
    let newValue: unknown = effectStr(convertNumber(value, name)) + ''
    const isDsec = typeof newValue === 'string' && newValue.includes('、')
    if (
      typeof newValue === 'string' &&
      isNaN(+newValue.charAt(newValue.length - 1)) &&
      name !== SUBSTANDARD_PROJECT &&
      value !== UNDEFINED_TARGET_TEXT
    ) {
      const strArr = newValue.split(newValue.charAt(newValue.length - 1))

      newValue = (
        <p>
          {strArr[0]}
          <span style={{ fontSize: FONT_SIZE_NORMAL }}>{newValue.charAt(newValue.length - 1)}</span>
        </p>
      )
    }

    const isText = value === UNDEFINED_TARGET_TEXT || name === SUBSTANDARD_PROJECT

    const memoReturnText = () => {
      return (
        <div
          ref={textRef}
          className={`card-middle ${isDsec ? 'doubleLine' : ''}`}
          style={{
            fontSize: isText ? FONT_SIZE_MEDIUM : FONT_SIZE_TITLE,
          }}
        >
          {newValue}
        </div>
      )
    }

    if (isOverflow) {
      const showTitle = () => {
        return <div style={{ maxWidth: CARD_MAX_WIDTH, whiteSpace: 'wrap' }}>{newValue}</div>
      }
      return (
        <Tooltip title={showTitle()} trigger={isDsec ? 'hover' : 'click'}>
          {memoReturnText()}
        </Tooltip>
      )
    } else {
      const showTitleValue = SHOW_LITTLE_ARR.includes(name || '') ? value : newValue
      const showTitle = () => {
        return <div style={{ maxWidth: CARD_MAX_WIDTH, whiteSpace: 'wrap' }}>{showTitleValue}</div>
      }
      return (
        <Tooltip title={showTitle()} trigger="hover">
          {memoReturnText()}
        </Tooltip>
      )
    }
  }, [value, name, isOverflow])

  const memoAdditional = useMemo(() => {
    const getValue = (item, index) => {
      if (typeof item === 'function') {
        return item()
      }

      if (index === 0) {
        return item
      }
      const num = parseFloat(item)
      const absValue = Math.abs(num)
      const isFm = [
        INDICATORS.DEVICE_WARRANTY_COUNT,
        INDICATORS.MAX_WARRANTY_COUNT,
        INDICATORS.ACCUMULATED_DELIVERY_COUNT,
        INDICATORS.ACCUMULATED_ACTIVATIONS,
      ].includes(name || '')
      const isNegative = isFm ? num >= 0 : num < 0
      const isValidValue = effectStr(absValue) !== PLACEHOLDER_STR && item !== INFINITY_SYMBOL
      const isInfinitySymbol = item === INFINITY_SYMBOL

      return (
        <div className="flex items-center">
          <span
            style={{
              fontWeight: FONT_WEIGHT_MEDIUM,
              fontSize: isInfinitySymbol ? FONT_SIZE_LARGE : FONT_SIZE_SMALL,
              color: `${
                num !== 0
                  ? !isValidValue || isInfinitySymbol
                    ? ''
                    : isNegative
                      ? CARD_STYLE.UP_COLOR
                      : CARD_STYLE.DOWN_COLOR
                  : ''
              }`,
            }}
          >
            {isInfinitySymbol
              ? INFINITY_SYMBOL
              : isValidValue
                ? `${(absValue * 100).toFixed(NUMBER_CONVERSION.DECIMAL_PLACES.STANDARD_PERCENT)}${
                    NUMBER_CONVERSION.PERCENTAGE_SYMBOL
                  }`
                : PLACEHOLDER_STR}
          </span>
          {num !== 0
            ? isValidValue &&
              !isInfinitySymbol && (
                <img
                  src={isNegative ? upIcon : downIcon}
                  alt={`${isNegative ? 'up' : 'down'}`}
                  style={{
                    width: CARD_IMAGE_SIZE,
                    height: CARD_IMAGE_SIZE,
                    alignSelf: 'center',
                    transform: isFm ? '' : TRANSFORM_ROTATE,
                  }}
                />
              )
            : '-'}
        </div>
      )
    }
    return (
      additional &&
      additional.length && (
        <div className="flex justify-between items-center h-10" style={{ lineHeight: LINE_HEIGHT }}>
          {additional.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center"
              style={{
                fontSize: FONT_SIZE_SMALL,
                color: index === 0 ? CARD_STYLE.GREY_TEXT_COLOR : CARD_STYLE.BLACK_TEXT_COLOR,
              }}
            >
              {getValue(item, index)}
            </div>
          ))}
        </div>
      )
    )
  }, [additional, name])

  return (
    <CardOuter
      lastCard={lastCard}
      className="card-box"
      style={{ flex: !additional?.length ? 2 : 1 }}
    >
      <CardMask>
        <CardContent>
          <TitleContainer>
            <Tooltip title={name}>{name}</Tooltip>
          </TitleContainer>
          {memoValue}
          {memoAdditional}
        </CardContent>
      </CardMask>
    </CardOuter>
  )
}

export interface CardLineProps {
  cardLineData: {
    name: string
    value: number | string
    additional: [string, string | number | undefined]
  }[]
}

const CardLine: React.FC<CardLineProps> = ({ cardLineData }) => {
  return (
    <div className="flex justify-between">
      {cardLineData.map((item, idx) => {
        return (
          <Card
            {...item}
            key={`card-${idx}-${item.name}`}
            lastCard={cardLineData.length - 1 === idx && cardLineData.length === 2}
          />
        )
      })}
    </div>
  )
}

export default memo(CardLine)
