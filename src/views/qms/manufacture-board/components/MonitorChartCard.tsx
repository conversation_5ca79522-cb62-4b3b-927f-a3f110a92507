/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, useEffect, useRef } from 'react'
import Table from '@hi-ui/table'
import * as echarts from 'echarts'
import { EMPTY_DATA_TEXT } from '../monitor-constant'
import { getAreaChartConfig } from '../echarts/monitor'
import '../monitor.scss'

interface TableColumn {
  title: string
  dataKey: string
  width?: number
  [key: string]: any
}

interface ChartData {
  x: string
  y: number
}

interface ChartStyle {
  color: string
  gradient: string
  label: string
}

interface MonitorChartCardProps {
  // 图表部分
  chartData: ChartData[]
  chartStyle: ChartStyle

  // 表格部分
  tableTitle: string
  columns: TableColumn[]
  tableData?: any[]
}

const MonitorChartCard: FC<MonitorChartCardProps> = ({
  chartStyle,
  chartData,
  tableTitle,
  columns: originalColumns,
  tableData = [],
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  // 确保第一列固定
  const columns = originalColumns.map((col, index) => ({
    ...col,
    fixed: index === 0 ? true : col.fixed,
    width: col.width || (index === 0 ? 150 : undefined),
  }))

  const chartInstance = useRef<echarts.ECharts>()

  useEffect(() => {
    if (!chartContainerRef.current || !chartData?.length || !chartStyle) return

    const chart = echarts.init(chartContainerRef.current)

    // 添加窗口大小变化时的自适应调整
    const handleResize = () => {
      chart.resize()
    }
    window.addEventListener('resize', handleResize)

    const option = getAreaChartConfig(chartData, chartStyle)

    chart.setOption(option)
    chartInstance.current = chart

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [chartData, chartStyle])

  return (
    <div className="monitor-chart-card">
      {/* 左侧图表部分 */}
      <div className="monitor-chart-card__chart-section">
        <div className="monitor-chart-card__chart-container" ref={chartContainerRef} />
      </div>

      {/* 右侧表格部分 */}
      <div className="monitor-chart-card__table-section">
        <div className="monitor-chart-card__title">{tableTitle}</div>
        <div className="monitor-chart-card__table-container">
          <Table
            columns={columns}
            data={tableData}
            bordered
            scrollbar={{ zIndex: 9 }}
            emptyContent={EMPTY_DATA_TEXT}
          />
        </div>
      </div>
    </div>
  )
}

export default MonitorChartCard
