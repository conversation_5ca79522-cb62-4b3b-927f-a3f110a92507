/* eslint-disable @typescript-eslint/no-explicit-any */
import { defaultChartLineConfig } from '@/views/qms/billboard/config'
import { CHART_SERIES_NAME, ECHART_COLORS } from '@/views/qms/manufacture-board/monitor-constant'
import * as echarts from 'echarts'

function tooltipFormatter(params) {
  // 兼容单轴pie图
  params = Array.isArray(params) ? params : [params]
  let tooltipStr = '<div style="border-radius: 6px;padding: 0 4px;">'

  if (params[0].axisValueLabel) {
    tooltipStr += `<p style="font-weight: 600; font-size: 14px; color: #1F2733;">${params[0].axisValueLabel}</p>`
  }

  params.forEach((item) => {
    const { value, name, seriesName, color } = item
    const mergeColor = typeof color === 'string' ? color : color?.colorStops?.[0]?.color ?? '#999'

    tooltipStr += `
      <div style="display: flex; font-size: 12px; justify-content: space-between; align-items: center; width: auto; margin-top: 8px;">
        <div style="width: 10px; height: 6px; border-radius: 1px; margin-bottom: 2px; background-color: ${mergeColor}; align-self: center;"></div>
        <div style="flex: 1; display: flex; align-items: center; color: #5F6A7A; margin: 0 14px 0 8px; text-align: left;">
          ${seriesName || name}
        </div>
        <div style="flex: 2; color: #1F2733; font-weight: 600; text-align: right; margin-right: 14px;">
          ${value}
        </div>
        ${
          item.percent !== undefined
            ? `<div style="flex:1; text-align: right;">
                <span style="color: #5F6A7A;">占比 ${item.percent}%</span>
              </div>`
            : ''
        }
      </div>`
  })

  tooltipStr += '</div>'
  return tooltipStr
}

export const getPieConfig = function (chartData, chartColors): echarts.EChartsOption {
  return {
    color: chartColors,
    grid: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20,
      containLabel: true,
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['55%', '75%'],
        center: ['15%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: chartData,
      },
    ],
    legend: [
      {
        type: 'plain',
        orient: 'vertical',
        right: 130,
        top: 'middle',
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 30,
        itemStyle: {
          borderWidth: 0,
        },
        formatter: function (name) {
          const item = chartData.find((d) => d.name === name)
          return item ? `{name|${name}}  {value|${item.value}}` : `{name|${name}}`
        },
        textStyle: {
          fontSize: 12,
          color: ECHART_COLORS.legendText,
          rich: {
            name: {
              color: ECHART_COLORS.legendText,
              fontSize: 12,
            },
            value: {
              color: ECHART_COLORS.legendValue,
              fontWeight: 500,
              fontSize: 12,
              lineHeight: 16,
            },
          },
        },
        data: ['研发人员', '测试人员', '运维人员'],
      },
      {
        type: 'plain',
        orient: 'vertical',
        right: 0,
        top: 55,
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 30,
        itemStyle: {
          borderWidth: 0,
        },
        formatter: function (name) {
          const item = chartData.find((d) => d.name === name)
          return item ? `{name|${name}}  {value|${item.value}}` : `{name|${name}}`
        },
        textStyle: {
          fontSize: 12,
          color: ECHART_COLORS.legendText,
          rich: {
            name: {
              color: ECHART_COLORS.legendText,
              fontSize: 12,
            },
            value: {
              color: ECHART_COLORS.legendValue,
              fontWeight: 500,
              fontSize: 12,
              lineHeight: 16,
            },
          },
        },
        data: ['管理人员', '产品人员'],
      },
    ],
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      trigger: 'item',
      formatter: tooltipFormatter,
      // formatter: (params: any) => {
      //   console.log('params :>> ', params)
      //   if (Array.isArray(params)) {
      //     return params
      //       .map((param) => {
      //         return `${param.seriesName}: ${param.value}`
      //       })
      //       .join('<br/>')
      //   } else {
      //     return `${params.seriesName}: ${params.value}`
      //   }
      // },
    },
    animation: true,
  }
}

export const getAreaChartConfig = function (chartData, chartStyle): echarts.EChartsOption {
  return {
    ...defaultChartLineConfig,
    title: {
      text: chartStyle.label,
      left: 10,
      top: 0,
      textStyle: {
        color: '#1f2733',
        fontSize: 14,
        fontWeight: 500,
      },
      padding: [0, 0, 0, 0],
    },
    legend: {
      data: [CHART_SERIES_NAME],
      right: 10,
      top: 0,
      textStyle: {
        color: ECHART_COLORS.legendText,
        fontSize: 12,
      },
      icon: 'roundRect',
      itemWidth: 10,
      itemHeight: 6,
      itemStyle: {
        borderRadius: 1,
        color: chartStyle.color,
        borderColor: chartStyle.color,
        borderWidth: 1,
      },
      itemGap: 12,
    },
    grid: {
      top: 50,
      left: 10,
      right: 10,
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.map((item) => item.x),
      axisLine: {
        lineStyle: {
          color: ECHART_COLORS.splitLine,
          width: 1,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: ECHART_COLORS.axis,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: ECHART_COLORS.splitLine,
          type: 'solid',
          width: 1,
        },
      },
      axisLabel: {
        color: ECHART_COLORS.axis,
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderRadius: 12,
      borderColor: 'rgba(235, 237, 240, 1)',
      borderWidth: 0.5,
      shadowColor: 'rgba(31, 39, 51, 0.06)',
      shadowOffsetX: 0,
      shadowOffsetY: 8,
      shadowBlur: 32,
      padding: [10, 15],
      textStyle: {
        color: '#1F2733',
      },
      formatter: tooltipFormatter,
    },
    series: [
      {
        name: CHART_SERIES_NAME,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          scale: true,
          focus: 'series',
          itemStyle: {
            color: '#fff',
            borderColor: chartStyle.color,
            borderWidth: 2,
          },
        },
        lineStyle: {
          width: 3,
          color: chartStyle.color,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: chartStyle.color + '80',
            },
            {
              offset: 1,
              color: chartStyle.color + '00',
            },
          ]),
        },
        data: chartData.map((item) => item.y),
      },
    ],
  }
}
