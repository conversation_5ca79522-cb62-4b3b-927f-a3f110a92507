// 文本常量
export const HEADER_TITLE = '生产质量看板'
export const PLACEHOLDER_STR = '-'
export const DATA_PRODUCTION_TIME_TEXT = '数据产出时间：'
export const INFINITY_SYMBOL = '∞'

// 页面维度
export const STATISTICAL_PAGE = {
  QUALITY: 1,
  MONITOR: 2,
}

// 数据维度
export const DIMENSION_TYPES = {
  PHONE: {
    key: 'deviceType',
    title: '手机',
  },
  REGION: {
    key: 'region',
    title: '版本/区域',
    tooltip: '版本控制“MFG DI”、区域控制“FPY&PY、OBA”',
  },
  FACTORY: {
    key: 'factory',
    title: '工厂',
  },
  PROJECT: {
    key: 'project',
    title: '项目',
  },
  FFR: {
    key: 'ffr',
    title: '制造FFR',
  },
}

// 卡片名称
export const CARD_NAME = {
  MFG_DI: 'MFG DI',
  FPY_PY: 'FPY&PY',
  OBA: 'OBA',
}

// 图表名称类型
export const BOARD_TYPE = {
  // MFG
  MFG_DI: 'MFG_DI',
  MFG_LEVEL: 'MFG_LEVEL',
  MFG_DOMAIN: 'MFG_DOMAIN',
  // FPY&OBA
  FPY: 'FPY',
  PY: 'PY',
  BAD_STATION: 'BAD_STATION',
  DPPM: 'DPPM',
  LAR: 'LAR',
  BAD_BEHAVIOR: 'BAD_BEHAVIOR',
  // FFR
  GENERAL: 'GENERAL',
  SPECIAL: 'SPECIAL',
  KEY_PROJECTS: 'KEY_PROJECTS',
}

// 板块名称常量
export const BOARD_NAME = {
  GENERAL: '大盘',
  SPECIAL: '三级故障',
  KEY_PROJECTS: '重点项目',
}

// 图表类型常量
export const CHART_TYPE = {
  LINE: 'line',
  BAR: 'bar',
  MFG_BAR: 'mfg-bar',
  MFG_PIE: 'mfg-pie',
  FPY_LINE: 'fpy-line',
  FPY_BAR: 'fpy-bar',
  GENERAL: 'general-line',
  SPECIAL: 'special-bar',
  KEY_PROJECTS: 'keyProjects-line',
}

// 卡片样式常量
export const CARD_STYLE = {
  TITLE_COLOR: '#1F2733',
  TITLE_FONT_SIZE: '12px',
  SUBTITLE_COLOR: '#5F6A7A',
  GREY_TEXT_COLOR: '#86909C',
  BLACK_TEXT_COLOR: '#000',
  UP_COLOR: '#00BB60',
  DOWN_COLOR: '#FF4A4A',
  ACTIVE_COLOR: '#237FFA',
  CARD_WIDTH: '128px',
  CARD_PADDING: '12px',
  CARD_BORDER_RADIUS: '8px',
  CARD_GAP: '8px',
}

// 图表容器ID
export const BOARD_CONTAINER_ID = 'qms-manufacture-board-0731'

// MFG DI 问题等级颜色
export const MFG_DI_LEVEL = {
  blocker: { color: '#FE7940' },
  critical: { color: '#FE9561' },
  major: { color: '#FEC833' },
  minor: { color: '#FEE789' },
}

export const FONT_SIZE_NORMAL = '14px'
export const FONT_SIZE_SMALL = '12px'
export const FONT_SIZE_MEDIUM = '16px'
export const FONT_SIZE_LARGE = '18px'
export const FONT_SIZE_TITLE = '20px'
export const FONT_WEIGHT_BOLD = '600'
export const FONT_WEIGHT_NORMAL = 'normal'
export const FONT_WEIGHT_MEDIUM = '500'
export const FONT_WEIGHT_REGULAR = '400'
export const CARD_IMAGE_SIZE = '16px'
export const CARD_BORDER_COLOR = '#EBEDF0'
export const TAG_BASE_COLOR = '#F2F4F7'

// 时间样式常量
export const DATA_PRODUCTION_TIME_STYLE = {
  fontSize: '12px',
  color: '#9299A6',
  fontWeight: 'normal',
}
// 产品线样式常量
export const BOARD_LINE_STYLE = {
  TITLE_BACKGROUNDCOLOR: '#f8fbfd',
  TITLE_FONT_WEIGHT: '600',
  TITLE_FONT_SIZE: '18px',
  TITLE_COLOR: '#1F2733',
  ICON_SIZE: '32px',
  ICON_MARGIN_RIGHT: '8px',
  PADDING_LEFT: '12',
  PADDING_HORIZONTAL: '8',
  PADDING_VERTICAL: '8',
  GAP: '19',
  HEIGHT: '32px',
  MARGIN_BOTTOM: '6',
  LABEL_COLOR: '#000',
  LABEL_FONT_SIZE: '14px',
}
