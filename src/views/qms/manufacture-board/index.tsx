import React, { useMemo, useState, useEffect, useCallback, ReactText } from 'react'
import { getTopSelect } from '@/api/qms/manufacture-board/quality'
import { microApp } from '@/utils/micro-app'
import CustomPortal from '@/components/custom-portal'
import { HEADER_TITLE_PORTAL } from '@/constants'
import { RadioGroup, RadioDataItem } from '@hi-ui/radio'
import Loading from '@hi-ui/loading'
import { useRequest } from 'ahooks'
import PrdQuality from './PrdQuality'
import PrdMonitor from './PrdMonitor'
import { BaseTabProps } from './interface'
import {
  HEADER_TITLE,
  DATA_PRODUCTION_TIME_STYLE,
  DATA_PRODUCTION_TIME_TEXT,
  PLACEHOLDER_STR,
  STATISTICAL_PAGE,
  BOARD_CONTAINER_ID,
} from './constant'
import './styles/main.scss'

const QmsPrdBoard: React.FC = () => {
  const [etlTime, setEtlTime] = useState<string>('')
  const [tabs, setTabs] = useState<BaseTabProps[]>([])
  const [statisticalType, setStatisticalType] = useState<RadioDataItem['id']>(
    STATISTICAL_PAGE.QUALITY
  )
  const [cacheKeys, setCacheKeys] = useState<number[]>([STATISTICAL_PAGE.QUALITY])

  // 初始化微应用设置
  useEffect(() => {
    const { setOpenSubMenu } = microApp.getProps() as {
      setOpenSubMenu: (value: boolean) => void
    }

    if (setOpenSubMenu) {
      setOpenSubMenu(false)
    }
  }, [])

  // 初始化数据
  const { loading } = useRequest(getTopSelect, {
    manual: false,
    onSuccess: ({ data }) => {
      if (data) {
        setTabs(data.tab || [])
        setEtlTime(data.etlTm || PLACEHOLDER_STR)
      }
    },
    onError: (err) => {
      console.log('🚀 ~ getTopSelect err:', err)
    },
  })

  // 切换统计类型处理函数
  const handleStatisticalChange = useCallback((value: ReactText) => {
    setCacheKeys((prev) => {
      const val = value as number
      if (!prev.includes(val)) prev.push(val)
      return prev
    })
    setStatisticalType(value)
  }, [])

  // 顶部导航栏组件
  const topBarComponent = useMemo(
    () => (
      <div className="w-full flex justify-between align-center">
        <div className="flex align-center gap-8">
          {HEADER_TITLE}
          <div style={DATA_PRODUCTION_TIME_STYLE}>{`${DATA_PRODUCTION_TIME_TEXT}${etlTime}`}</div>
        </div>
        <div className="flex justify-end items-center">
          <RadioGroup
            className="prd-statistical-radio"
            value={statisticalType}
            type="button"
            data={tabs}
            onChange={handleStatisticalChange}
          />
        </div>
      </div>
    ),
    [tabs, etlTime, statisticalType, handleStatisticalChange]
  )

  // 加载状态组件
  const loadingComponent = (
    <div className="w-full h-full flex justify-center items-center">
      <Loading size="lg" />
    </div>
  )

  // 主内容组件-页面状态保留
  const mainContent = useMemo(() => {
    const contents = [
      {
        key: STATISTICAL_PAGE.QUALITY,
        render: (props) => <PrdQuality {...props} />,
      },
      {
        key: STATISTICAL_PAGE.MONITOR,
        render: (props) => <PrdMonitor {...props} />,
      },
    ]
    // 匹配当前页面属性数据
    const pageProps = tabs?.find((item) => item.id === statisticalType)
    return (
      <>
        <CustomPortal domId={HEADER_TITLE_PORTAL}>{topBarComponent}</CustomPortal>
        {contents.map((item) => {
          const isLoaded = cacheKeys.includes(item.key)
          return (
            <div
              key={item.key}
              style={{ display: statisticalType === item.key ? 'block' : 'none' }}
            >
              {isLoaded && item.render(pageProps)}
            </div>
          )
        })}
      </>
    )
  }, [cacheKeys, statisticalType, tabs, topBarComponent])

  return (
    <div className="w-full h-full prd-board" id={BOARD_CONTAINER_ID}>
      {loading ? loadingComponent : mainContent}
    </div>
  )
}

QmsPrdBoard.displayName = 'QmsPrdBoard'

export default QmsPrdBoard
