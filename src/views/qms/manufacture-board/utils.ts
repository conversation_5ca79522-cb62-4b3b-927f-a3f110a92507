import moment from 'moment'

/**
 * 获取上周一
 * @returns  时间戳(毫秒)
 */
export function getLastWeek() {
  const currentWeek = moment().startOf('isoWeek')
  const lastWeek = moment().subtract(1, 'week').startOf('isoWeek')
  let targetDate
  if (currentWeek.year() !== lastWeek.year()) {
    targetDate = currentWeek
  } else {
    targetDate = lastWeek
  }
  return targetDate.valueOf()
}

/**
 * 获取上月1号
 * @returns 时间戳（毫秒）
 */
export function getLastMonth() {
  const currentDate = moment().startOf('month')
  const lastMonth = moment().subtract(1, 'month').startOf('month')
  const currentMonth = moment().month() + 1
  let targetDate
  if (currentMonth === 1) {
    targetDate = currentDate
  } else {
    targetDate = lastMonth
  }
  return targetDate.valueOf() // 返回时间戳（毫秒）
}

/**
 * 格式化日期
 * @returns string
 */
export function formatDateGeneral(data, str = 'YYYY-MM-DD') {
  if (!data) return undefined
  return moment(data).format(str)
}

/**
 * 格式化字符串首字母大写
 * @param str 输入字符串
 * @returns 首字母大写的字符串
 */
export const formatFirstToUpperCase = (str = '') => {
  if (!str || typeof str !== 'string') return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}
