@import './_variables';

@font-face {
  font-family: MiSans-Medium;
  src: url('https://cdn.cnbj1.fds.api.mi-img.com/scm/qms/work-bench/family/MiSans-Medium.ttf')
    format('truetype');
}

.prd-statistical-radio {
  padding: 2px;
  font-weight: normal;
  color: #5f6a7a;
  background-color: #f6f7f9;
  border-radius: 6px;

  .hi-v4-radio--type-button[data-checked] {
    box-sizing: border-box;
    color: #000;
    background-color: #fff;
    border-radius: 4px;
    font-weight: 500;
  }

  .hi-v4-radio--type-button::after {
    display: none;
  }
}

.prd-board {
  // 设计稿-最小宽
  min-width: 1356px;
}

.prd-boardline {
  height: auto;
  min-width: 100%;
  background-color: rgb(255 255 255 / 65%);
  overflow: visible hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.prd-boardcell {
  padding-bottom: 20px;
}

.prd-select-more {
  --hi-v4-color-gray-300: #ebedf0;
  --hi-v4-color-gray-700: #5f6a7a;

  &.active {
    --hi-v4-color-gray-300: #bde2ff;
    --hi-v4-color-gray-700: #237ffa;
    --hi-v4-color-static-white: #e2f3fe;
  }
}

.nav-button-filled {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: $bg-color-light;
  font-size: $font-size-14;
  font-weight: $font-weight-400;
  line-height: 20px;
  color: $text-color;

  &:hover {
    cursor: pointer;
  }

  &.active {
    background-color: #e2f3fe;
    color: $text-color-active;
  }
}

.nav-button-text {
  font-size: $font-size-14;
  font-weight: $font-weight-500;
  line-height: 20px;
  color: $color-secondary;
  cursor: pointer;

  &.active {
    color: $text-color-active;
  }
}
