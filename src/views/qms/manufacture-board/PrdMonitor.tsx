import React, { useState, useMemo } from 'react'
import BoardLine from './components/BoardLine'
import type { ActiveNavBar } from './interface'
import MonitorTitle from './components/MonitorTitle'
import MonitorPerson from './components/MonitorPerson'
import KeyMetrics from './components/KeyMetrics'
import InvalidTable from './components/InvalidTable'
import MonitorChartCard from './components/MonitorChartCard'
import MonitorCompliance from './components/MonitorCompliance'
import {
  DOC_COLS,
  MACH_COLS,
  CHART_CARD_CONSTANTS,
  personData,
  PERSON_CHART_TEXTS,
  TOOLTIP_TEXTS,
} from './monitor-constant'
import { createBoardItemData } from './PrdQuality'
import './monitor.scss'
import {
  data1,
  data2,
  MetricsData,
  mockDocData,
  mockMachineData,
  mockComplianceData,
} from './mock-data'
import {
  bigFieldIcon as BigFieldIcon,
  keyProIcon as KeyProIcon,
  specialIcon as SpecialIcon,
} from '@/views/qms/billboard/assets'
import { bar<PERSON>hart as Bar<PERSON><PERSON>, personCard as PersonCard } from './assets'

const data = createBoardItemData()
const boardLineData = data.find((item) => item.productLineName === '手机')!

const PrdMonitor: React.FC = () => {
  const [activeNavBar, setActiveNavBar] = useState<ActiveNavBar>(boardLineData.defaultNavBar)

  const filteredMachineData = useMemo(
    () =>
      mockMachineData.filter(
        (item) => item.factory === activeNavBar.factory && item.project === activeNavBar.project
      ),
    [activeNavBar.factory, activeNavBar.project]
  )

  const filteredDocData = useMemo(
    () =>
      mockDocData.filter(
        (item) => item.factory === activeNavBar.factory && item.project === activeNavBar.project
      ),
    [activeNavBar.factory, activeNavBar.project]
  )

  const handleNavChange = (newNavBar: ActiveNavBar) => {
    console.log('获取到筛选数据:', newNavBar)
    setActiveNavBar(newNavBar)
  }

  return (
    <div className="prd-monitor">
      <div className="prd-monitor__filter">
        <BoardLine
          data={boardLineData}
          renderContent={(params) => {
            handleNavChange(params.activeNavBar)
            return null
          }}
        />
      </div>
      <div className="prd-monitor__content">
        {/* 人-检验员 */}
        <MonitorTitle icon={<PersonCard />} title="人-检验员" topPadding={12} />
        <MonitorPerson {...personData} />
        {/* 机-关键参数 */}
        <MonitorTitle icon={<SpecialIcon />} title="机-关键参数" />
        <div className="monitor-card">
          <KeyMetrics metricsData={MetricsData} formulaTooltip={TOOLTIP_TEXTS.MACHINE_COMPLIANCE} />
          <InvalidTable columns={MACH_COLS} tableData={filteredMachineData} maxHeight={480} />
        </div>
        {/* 物料 */}
        <MonitorTitle
          icon={<BigFieldIcon />}
          title="物料LAR/物料VLRR"
          tooltip={TOOLTIP_TEXTS.MATERIAL}
        />
        <div className="materials-box">
          <MonitorChartCard
            chartStyle={CHART_CARD_CONSTANTS.MATERIAL_LAR}
            tableTitle={CHART_CARD_CONSTANTS.MATERIAL_LAR.label}
            columns={[]}
            chartData={data1}
          />
          <div className="materials-box__line" />
          <MonitorChartCard
            chartStyle={CHART_CARD_CONSTANTS.MATERIAL_VLRR}
            tableTitle={CHART_CARD_CONSTANTS.MATERIAL_VLRR.label}
            columns={[]}
            chartData={data2}
          />
        </div>
        {/* 生产文件 */}
        <MonitorTitle icon={<KeyProIcon />} title="法-生产文件" />
        <div className="monitor-card">
          <KeyMetrics metricsData={MetricsData} formulaTooltip={TOOLTIP_TEXTS.DOC_COMPLIANCE} />
          <InvalidTable columns={DOC_COLS} tableData={filteredDocData} maxHeight={480} />
        </div>
        {/* 符合率监控 */}
        <MonitorTitle
          icon={<BarChart />}
          title="环-符合率"
          subtitle={PERSON_CHART_TEXTS.SUB_TITLE}
        />
        <MonitorCompliance environment={mockComplianceData} />
      </div>
    </div>
  )
}

export default React.memo(PrdMonitor)
