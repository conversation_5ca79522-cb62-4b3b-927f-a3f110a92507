/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode } from 'react'
import { DefaultTheme } from 'styled-components'

// 筛选项数据
export interface BaseTabProps {
  id: number | string
  title: string
  flag: string
  trPhase: null | string
  child: null | BaseTabProps[]
}

// 项目信息接口
export interface ProjectItem {
  projectCode: string
  withinTwoWeek: number
  remainDays: number | null
  nextTr: string
  marketDays: number | null
  indexNodeList: string[]
}

// 图表主题接口
export interface ChartTheme {
  color: string[] | Record<string, string | string[]>
  icons?: string[]
}
export interface AppTheme extends DefaultTheme {
  color: string
  background: string
  borderColor: string
  chart: ChartTheme
}

// 通用图表数据项接口
export interface ChartDataItem {
  name: string
  value: number
  color?: string
  // 扩展字段，用于特殊数据处理
  fzNum?: number
  fmNum?: number
  isGeneral?: boolean
}

// 通用图表数据结构接口
export interface ChartData<T extends ChartDataItem = ChartDataItem> {
  chartData: {
    dataList: T[]
  }
}

// 图表配置接口
export interface ChartConfig {
  chartType: string
  theme?: ChartTheme
  extraLine?: { key: string; text: string }[]
  width?: string
  height?: string
}

// 特定图表数据类型
export type PieChartDataItem = ChartDataItem
export type BarChartDataItem = ChartDataItem
export type LineChartDataItem = ChartDataItem

// 特定图表数据结构类型
export type PieChartData = ChartData<PieChartDataItem>
export type BarChartData = ChartData<BarChartDataItem>
export type LineChartData = ChartData<LineChartDataItem>
// ffr看板数据结构类型
export interface FfrChartData {
  ffrRate: number
  ffrYoy: number
  fzNum: number
  fzYoy: number
  fmNum: number
  fmYoy: number
  target?: number | { index: number; value: number }[]
  targetChallenge?: number | { index: number; value: number }[]
  trendChartData: any[]
  barChartData?: any[]
}

// 导航栏激活状态类型
export interface ActiveNavBar {
  deviceType: string
  region: string
  factory?: string
  project?: string
  date?: string
}

// 产品线基础数据接口
export interface BaseDataProps {
  productLineName: string
  productLineId: number
  deviceTypes: string[] // 更灵活的设备类型，支持扩展
  dimensions: DimensionProp[]
  defaultNavBar: ActiveNavBar
}

// 维度属性接口
export interface DimensionProp {
  key: string // 更灵活的键类型，支持扩展
  title: string
  list: string[]
  limit?: number
  tooltip?: string
}
// 内容参数接口
export interface ContentParams {
  activeNavBar: ActiveNavBar
  data: BaseDataProps
}

// 看板项数据接口
export interface BoardItemData extends BaseDataProps {
  renderContent: (params: ContentParams) => ReactNode
}

// 看板线组件属性接口
export interface PrdBoardLineProps {
  tab: BaseDataProps
  renderContent: (params: ContentParams) => ReactNode
}

// 产品线看板属性接口
export interface PrdBoardProps {
  deviceTypes: string[] // 更灵活的设备类型，支持扩展
  dimensions: DimensionProp[]
}
// 导航栏组件属性接口
export interface NavBarProps extends PrdBoardProps {
  activeNavBar: ActiveNavBar
  setActiveNavBar: (activeNavBar: ActiveNavBar, key: string) => void
}
