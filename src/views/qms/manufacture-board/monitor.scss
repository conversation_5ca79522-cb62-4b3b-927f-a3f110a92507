.prd-monitor {
  background-color: #fff;
  border-radius: 12px;

  &__content {
    font-size: 14px;
    font-weight: 400;
    color: #5f6a7a;
    padding: 0 12px 12px;
  }
}

.materials-box {
  padding: 12px;
  border: 1px solid #ebedf0;
  border-radius: 12px;
  margin: 0 !important;

  &__line {
    border-color: #dfe2e8;
    border: 1.5px dashed #dfe2e8;
    height: 0;
    margin: 12px 0;
  }
}

.monitor-person {
  display: flex;
  height: 388px;
  gap: 16px;
  margin: 0;

  .ability-board {
    flex: 2;
    min-width: 878px;
    border: 1px solid #ebedf0;
    border-radius: 12px;
    overflow: hidden;
  }

  &__right {
    flex: 1;
    min-width: 430px;
    display: flex;
    flex-direction: column;
    border: 1px solid #ebedf0;
    border-radius: 12px;
    padding: 20px 16px;
    gap: 16px;

    .staff-reserve {
      height: 140px;
      min-width: 398px;
      border: 1px solid #ebedf0;
      border-radius: 12px;
      padding: 20px 16px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;

      &__info-icon {
        margin-left: 4px;
        cursor: pointer;
        color: #5f6a7a;

        &:hover {
          color: #1f2733;
        }
      }

      &__title {
        font-size: 16px;
        font-weight: 500;
        color: #1f2733;
        display: inline-flex;
        align-items: center;
      }

      &__content {
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        color: #1f2733;
      }

      &__tip {
        display: flex;
        align-items: center;
        font-size: 12px;

        &-title {
          font-weight: 400;
          line-height: 20px;
          color: #86909c;
          margin-right: 16px;
          display: flex;
          align-items: center;
        }

        &-value {
          font-weight: 500;
          line-height: 20px;
          color: #1f2733;
        }

        &-line {
          width: 1px;
          height: 12px;
          background-color: #dfe2e8;
          border-radius: 2px;
          margin: 0 16px;
        }
      }
    }

    .staff-distribution {
      height: 192px;
      min-width: 398px;
      border: 1px solid #ebedf0;
      background-color: rgb(231 240 255 / 40%);
      border-radius: 12px;
      padding: 12px 20px;

      &__title {
        font-size: 16px;
        font-weight: 500;
        color: #1f2733;
        margin-bottom: 12px;
      }

      &__content {
        height: calc(100% - 32px);
      }
    }
  }
}

.monitor-card {
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 16px;
  margin: 0 !important;

  &__divider {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    margin: 0 0 16px;
    color: #1f2733;
  }

  &__metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .metric-item {
      flex: 1;
      min-width: 120px;
      padding: 12px 16px;
      border-radius: 12px;
      border: 1px solid #ebedf0;
      background-color: #fff;
      margin-right: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &:last-child {
        margin-right: 0;
      }

      &__label {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #1f2733;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
      }

      &__info-icon {
        margin-left: 4px;
        cursor: pointer;
        color: #5f6a7a;
        display: inline-flex;
        align-items: center;

        &:hover {
          color: #1f2733;
        }
      }

      &__value-container {
        display: flex;
        align-items: baseline;
        color: #1f2733;
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
      }

      // 状态样式
      &--normal {
        .metric-item__value {
          color: #52c41a;
        }
      }

      &--warning {
        .metric-item__value {
          color: #faad14;
        }
      }

      &--error {
        .metric-item__value {
          color: #f5222d;
        }
      }
    }
  }

  &__table {
    // 确保表格区域有足够的空间
    min-height: 200px;

    // 设置表格行高为40px
    .hi-table__row {
      height: 40px !important;
      line-height: 40px !important;
    }

    .hi-v4-table-header-cell {
      height: 20px !important;
      line-height: 20px !important;
      padding: 10px 12px !important;
    }

    .hi-v4-table-cell {
      height: 20px !important;
      line-height: 20px !important;
      padding: 10px 12px !important;
    }
  }
}

.monitor-compliance {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin: 0 !important;

  .compliance-item {
    flex: 1;
    min-width: 120px;
    padding: 16px;
    border-radius: 12px;
    border: 1px solid #ebedf0;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    &__title {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #1f2733;
      display: flex;
      align-items: center;
    }

    &__info-icon {
      margin-left: 4px;
      cursor: pointer;
      color: #5f6a7a;
      display: inline-flex;
      align-items: center;

      &:hover {
        color: #1f2733;
      }
    }

    &__value-container {
      display: flex;
      align-items: baseline;
      color: #1f2733;
      font-size: 24px;
      font-weight: 600;
      line-height: 32px;
      margin-bottom: 12px;
    }

    &__value {
      color: #52c41a;
    }

    &__unit {
      font-size: 14px;
      margin-left: 4px;
      color: #5f6a7a;
    }

    &__extra-info {
      display: flex;
      align-items: center;
      font-size: 12px;
    }

    &__extra-item {
      display: flex;
      align-items: center;
    }

    &__extra-label {
      font-weight: 400;
      line-height: 20px;
      color: #86909c;
      margin-right: 4px;
    }

    &__extra-value {
      font-weight: 500;
      line-height: 20px;
      color: #1f2733;
    }

    &__extra-divider {
      width: 1px;
      height: 12px;
      background-color: #dfe2e8;
      border-radius: 2px;
      margin: 0 12px;
    }

    // 状态样式
    &--normal {
      .compliance-item__value {
        color: #52c41a;
      }
    }

    &--warning {
      .compliance-item__value {
        color: #faad14;
      }
    }

    &--error {
      .compliance-item__value {
        color: #f5222d;
      }
    }
  }
}

/* 新的符合率监控组件样式 */
.monitor-compliance-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* 四个模块的横向布局 */
  .compliance-modules {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px; // 为滚动条留出空间
  }

  @media (max-width: 1200px) {
    .compliance-modules {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .compliance-modules {
      grid-template-columns: 1fr;
    }
  }

  /* 单个模块容器 */
  .compliance-module-container {
    border: 1px solid #ebedf0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    min-width: 280px;
    flex: 1;
    height: 432px;
    padding: 20px 16px;
    gap: 12px;
  }

  /* 标题栏样式 */
  .compliance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 0;
    background-color: transparent;
    margin-bottom: 12px;

    &__left {
      display: flex;
      align-items: center;
      height: 100%;
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-right: 12px;
    }

    &__icon-circle {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__actual {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }

    &__title {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #1f2733;
    }

    &__value {
      font-size: 24px;
      font-weight: 600;
      line-height: 28px;

      &--met {
        color: #08a351;
      }

      &--unmet {
        color: #ff5959;
      }
    }

    &__target {
      display: flex;
      align-items: flex-end;
      height: 100%;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5f6a7a;
    }
  }

  /* 内容栏样式 */
  .compliance-content {
    height: 328px;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e7f0ff;
    background-color: rgb(244 251 255 / 40%); /* #F4FBFF66 */
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow-y: auto;

    /* 单个模块的样式 - 简化后只保留header */
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 24px;
    }

    &__title {
      font-size: 14px;
      font-weight: 500;
      color: #1f2733;
      line-height: 24px;
    }

    &__standard {
      font-size: 12px;
      font-weight: 500;
      color: #5f6a7a;
      line-height: 20px;
    }

    &__rankings {
      display: flex;
      flex-direction: column;
      gap: 8px;
      height: 100%;
    }
  }

  /* 排名项样式 */
  .ranking-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 24px;
    position: relative;

    &__index {
      width: 14px;
      height: 14px;
      border-radius: 3px;
      padding: 1px 4px;
      font-weight: 500;
      font-size: 11px;
      line-height: 12px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px; /* 名称距离序号栏12px */
      // 背景色现在通过React组件中的内联样式设置
    }

    &__name {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #1f2733;
      margin-right: auto; /* 名称自动填充剩余空间 */
    }

    &__value {
      position: absolute;
      right: 87px; /* 距离右侧87px */
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #1f2733;
    }

    &__trend {
      position: absolute;
      right: 9px; /* 距离右侧9px */
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__trend-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      &--up {
        color: #52c41a;
      }

      &--down {
        color: #f5222d;
      }

      &--stable {
        color: #5f6a7a;
      }
    }

    /* 不显示trend时的样式 */
    &--no-trend {
      .ranking-item__value {
        right: 16px; /* 距离右侧16px */
      }
    }
  }
}

.monitor-chart-card {
  display: flex;
  width: 100%;
  gap: 16px;

  &__chart-section {
    flex: 1;
    min-height: 300px;
    border-radius: 4px;
    background-color: #fff;
    padding: 8px 16px 16px;
    display: flex;
    flex-direction: column;
  }

  &__table-section {
    flex: 1;
    border-radius: 4px;
    background-color: #fff;
    padding: 8px 16px 16px;
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #1f2733;
  }

  &__chart-container {
    flex: 1;
    width: 100%;
    height: 100%;
    min-height: 250px;

    // 默认值
    --chart-color: #0189ff;
    --chart-gradient: linear-gradient(
      180deg,
      rgb(1 111 254 / 13%) 11.1%,
      rgb(1 143 255 / 1.3%) 74.34%,
      rgb(1 159 255 / 0%) 105.44%
    );
  }

  &__table-container {
    flex: 1;
    width: 100%;
  }
}
