import { request } from '@/utils/request'

const prefix = '/api/dashboard/manufacturing'
const apiPrefix = `${window.location.origin}/api/qms${prefix}`

/**
 * 页面筛选
 * @returns {Promise<Object>} 页面筛选数据
 */
export const getTopSelect = () => {
  return request({
    url: `${apiPrefix}/init`,
    method: 'GET',
    enableLang: false,
    skipApiHost: true,
  })
}

/**
 * mfgdi数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} mfgdi数据
 */
export const getMfgdiData = (params) => {
  return request({
    url: `${apiPrefix}/index/mfgdi`,
    method: 'POST',
    data: params,
  })
}

/**
 * 未关闭问题明细
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 分页数据
 */
export const getIssuedetails = (params) => {
  return request({
    url: `${apiPrefix}/index/issuedetails`,
    method: 'POST',
    data: params,
  })
}

/**
 * FPY&OBA
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} fpy与oba数据
 */
export const getPyobaData = (params) => {
  return request({
    url: `${apiPrefix}/index/pyoba`,
    method: 'POST',
    data: params,
  })
}

/**
 * ffr筛选
 * @returns {Promise<Array>} ffr筛选数据
 */
export const getFfrSelect = () => {
  return request({
    url: `${apiPrefix}/index/ffr_init`,
    method: 'GET',
    enableLang: false,
    skipApiHost: true,
  })
}

/**
 * ffr数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 制造ffr数据
 */
export const getFfrStatistics = (params) => {
  return request({
    url: `${apiPrefix}/index/ffr_statistics`,
    method: 'POST',
    data: params,
  })
}

/**
 * ffr数据-项目，对比
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 制造ffr数据（重点项目、三级故障）
 */
export const getFfrProjects = (params) => {
  return request({
    url: `${apiPrefix}/index/ffr_project`,
    method: 'POST',
    data: params,
  })
}
